#!/usr/bin/env python3

import requests
import json
import time

def test_vasp_logo():
    """Test VASP logo detection with timeout handling"""
    print("🔍 Testing VASP Logo Detection")
    print("=" * 40)
    
    url = "http://localhost:8000/api/query"
    
    # Test query
    query = "VASP Enterprises logo"
    
    payload = {
        "query": query,
        "model": "gemini-2.0-flash",
        "fallback_enabled": True
    }
    
    print(f"Query: {query}")
    print("Sending request...")
    
    try:
        # Send request with 30 second timeout
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Answer: {data.get('answer', 'No answer')[:200]}...")
            print(f"Document sources: {len(data.get('document_sources', []))}")
            print(f"Website sources: {len(data.get('website_sources', []))}")
            print(f"LLM fallback used: {data.get('llm_fallback', False)}")
            
            # Check if visual content was found
            if data.get('visual_content_found'):
                print(f"🎯 Visual content found: {data.get('visual_content_types', [])}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 30 seconds")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_endpoints():
    base_url = "http://127.0.0.1:8000"
    
    print("Testing Health Endpoint...")
    try:
        response = requests.get(f"{base_url}/api/health")
        print(f"✅ Health: {response.status_code}")
    except:
        print("❌ Health: Failed")
        return
    
    print("\nTesting Website Endpoints...")
    try:
        websites = requests.get(f"{base_url}/api/websites").json()
        if websites:
            website_id = websites[0]['id']
            
            # Test extraction details
            response = requests.get(f"{base_url}/api/websites/{website_id}/extraction-details")
            print(f"✅ Website Extraction Details: {response.status_code}")
            
            # Test content
            response = requests.get(f"{base_url}/api/websites/{website_id}/content")
            print(f"✅ Website Content: {response.status_code}")
        else:
            print("⚠️ No websites to test")
    except Exception as e:
        print(f"❌ Website tests failed: {e}")
    
    print("\nTesting Document Endpoints...")
    try:
        documents = requests.get(f"{base_url}/api/documents").json()
        if documents:
            document_id = documents[0]['id']
            
            # Test extraction details
            response = requests.get(f"{base_url}/api/documents/{document_id}/extraction-details")
            print(f"✅ Document Extraction Details: {response.status_code}")
            
            # Test content
            response = requests.get(f"{base_url}/api/documents/{document_id}/content")
            print(f"✅ Document Content: {response.status_code}")
        else:
            print("⚠️ No documents to test")
    except Exception as e:
        print(f"❌ Document tests failed: {e}")

if __name__ == "__main__":
    test_vasp_logo()
    test_endpoints() 