import os
import re
import logging
import numpy as np
import shutil
import time
import uuid
import json
import random
import hashlib
from datetime import datetime
from uuid import uuid4
from functools import lru_cache
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Depends, Request, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, RedirectResponse
from fastapi.concurrency import run_in_threadpool
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import custom modules
from website_scraper import extract_website_text
from document_extractor import extract_document, extract_document_with_visual_content
from vector_db import vector_db  # Import the vector database
import llm_router  # Import the new LLM router module
from feedback import FeedbackData, send_feedback_email, get_feedback_emails, update_feedback_emails, FeedbackEmailConfig  # Import feedback module
from supabase_client import supabase  # Import Supabase client
from config import config  # Import secure configuration
import vector_search  # Import standardized vector search

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import optional components with graceful fallbacks
try:
    from database_optimizer import DatabaseOptimizer
    DATABASE_OPTIMIZATION_AVAILABLE = True
except ImportError:
    DATABASE_OPTIMIZATION_AVAILABLE = False
    logger.warning("Database optimization not available")

try:
    from memory_manager import MemoryManager
    memory_manager = MemoryManager()
    MEMORY_MANAGEMENT_AVAILABLE = True
except ImportError:
    MEMORY_MANAGEMENT_AVAILABLE = False
    logger.warning("Memory management not available")

try:
    from performance_optimizer import PerformanceManager
    PERFORMANCE_OPTIMIZATION_AVAILABLE = True
except ImportError:
    PERFORMANCE_OPTIMIZATION_AVAILABLE = False
    logger.warning("Performance optimization not available")

try:
    from health_check import HealthCheckManager, DatabaseHealthCheck, MemoryHealthCheck, DiskHealthCheck, VectorSearchHealthCheck
    health_manager = HealthCheckManager()
    HEALTH_CHECK_AVAILABLE = True
except ImportError:
    HEALTH_CHECK_AVAILABLE = False
    logger.warning("Health check system not available")

# Define pagination params for backward compatibility
class PaginationParams(BaseModel):
    page: int = 1
    page_size: int = 50
    sort_by: str = "created_at"
    sort_order: str = "DESC"
    search: Optional[str] = None
    category: Optional[str] = None
    status: Optional[str] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None

# Load environment variables
load_dotenv()

# PERFORMANCE OPTIMIZATION: Query caching for faster responses
QUERY_CACHE = {}
EMBEDDING_CACHE = {}
CACHE_TTL = 300  # 5 minutes cache TTL

def get_cache_key(query: str, model: str = "") -> str:
    """Generate a cache key for query results."""
    return hashlib.md5(f"{query}_{model}".encode()).hexdigest()

@lru_cache(maxsize=100)
def cached_embedding_generation(query: str, model_id: str) -> Optional[List[float]]:
    """Cache embeddings for frequently asked queries."""
    try:
        return generate_embedding(query, model_id)
    except Exception as e:
        logger.error(f"Error generating cached embedding: {e}")
        return None

# Direct document search functions
def search_documents_by_title(query: str, limit: int = 5):
    """Search for documents by title."""
    logger.info(f"Searching for documents with title containing '{query}'...")

    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")
        return []

def get_document_chunks(document_id: str, limit: int = 10):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")

    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []

        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 10):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")

    # Extract key terms for better search
    import re
    from string import punctuation

    # Check if this is an acronym search (e.g., "full form of ACP")
    acronym_match = re.search(r'(?:full\s+form\s+of|what\s+(?:does|is|are)\s+the\s+full\s+form\s+of|what\s+(?:does|is|are)\s+)\s*([A-Z]{2,})', query, re.IGNORECASE)

    if acronym_match:
        # This is an acronym search, extract the acronym
        acronym = acronym_match.group(1).upper()
        logger.info(f"Detected acronym search for: {acronym}")

        # Special handling for acronym searches
        return search_for_acronym(acronym, limit)

    # Clean the query and extract key terms
    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    # If no key terms found, use the original query
    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for content search: {key_terms}")

    # Create a tsquery string with OR operators between terms for broader matches
    ts_query_terms = " | ".join([term.replace("'", "''") for term in key_terms])

    # Sanitize the full query for logging
    sanitized_full_query = query.replace("'", "''")

    # Prepare the query with both exact phrase matching and key term matching
    # Join with documents table to get proper document information
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        COALESCE(d.display_name, d.file_name, d.name, 'Unknown Document') as filename,
        d.file_path as url,
        'document' as source_type,
        GREATEST(
            ts_rank(to_tsvector('english', dc.text), to_tsquery('english', '{ts_query_terms}')),
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{sanitized_full_query}'))
        ) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ to_tsquery('english', '{ts_query_terms}')
        OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{sanitized_full_query}')
        OR dc.text ILIKE '%{sanitized_full_query}%'
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        return []

def search_for_acronym(acronym: str, limit: int = 5):
    """Special search function for acronyms."""
    logger.info(f"Performing specialized acronym search for: {acronym}")

    # Sanitize the acronym
    sanitized_acronym = acronym.replace("'", "''")

    # Pattern 1: Look for exact acronym followed by words in parentheses (e.g., "ACP (Alarm Chain Pulling)")
    # Pattern 2: Look for exact acronym followed by "stands for" or "means" or "is"
    # Pattern 3: Look for exact acronym followed by a dash or colon and then words
    # Pattern 4: Look for the words "full form" near the acronym
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        1.0 AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)'
        OR dc.text ~* '\\b{sanitized_acronym}\\s+(stands\\s+for|means|is)\\b'
        OR dc.text ~* '\\b{sanitized_acronym}\\s*[-:]'
        OR (dc.text ~* '\\b{sanitized_acronym}\\b' AND dc.text ~* 'full\\s+form')
        OR dc.text ~* '\\b{sanitized_acronym}\\b.{{0,30}}(stands\\s+for|means|refers\\s+to|is)'
    ORDER BY
        CASE
            WHEN dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)' THEN 1
            WHEN dc.text ~* 'full\\s+form.{{0,20}}\\b{sanitized_acronym}\\b' THEN 2
            WHEN dc.text ~* '\\b{sanitized_acronym}\\b.{{0,20}}full\\s+form' THEN 3
            ELSE 4
        END
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching for acronym: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching acronym '{acronym}'")

        # If no results found with the specialized search, try a more general search
        if not result:
            logger.info(f"No specialized matches for acronym '{acronym}', trying general search")

            # Fallback to a more general search
            general_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ~* '\\b{sanitized_acronym}\\b'
            LIMIT {limit}
            """

            result = supabase.execute_query(general_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with general acronym search: {result['error']}")
                return []

            logger.info(f"Found {len(result)} document chunks with general acronym search")

        return result
    except Exception as e:
        logger.error(f"Error searching for acronym: {str(e)}")
        return []

def text_based_document_search(query: str, top_k: int = 10):
    """
    Text-based document search using keyword matching with relevance scoring.
    Works with in-memory DOCUMENT_CHUNKS without requiring embeddings.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using text-based document search for: '{query}'")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Railway domain keywords for relevance boost
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection'
        }

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching (must have multiple matches for general terms)
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                # Require more matches for shorter queries
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5

            # 3. Railway domain relevance (strong boost)
            railway_matches = railway_keywords & chunk_words
            if railway_matches:
                score += len(railway_matches) * 0.8

            # 4. Special boost for specific technical terms
            if 'fsds' in chunk_text:
                score += 1.5
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 1.0

            # 5. Penalty for very generic matches
            generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
            if query_words.issubset(generic_terms):
                score *= 0.1  # Heavy penalty for generic queries

            # Only include chunks with meaningful relevance (increased threshold)
            if score >= 0.8:  # Much higher threshold
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"Text-based document search found {len(result)} relevant chunks (threshold: 0.8)")

        return result

    except Exception as e:
        logger.error(f"Error in text-based document search: {str(e)}")
        return []

def text_based_website_search(query: str, top_k: int = 10):
    """
    Text-based website search using keyword matching with relevance scoring.
    Works with in-memory DOCUMENT_CHUNKS without requiring embeddings.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using text-based website search for: '{query}'")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Transport/railway domain keywords for relevance boost
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services'
        }

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching (must have multiple matches for general terms)
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                # Require more matches for shorter queries
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5

            # 3. Transport domain relevance (strong boost)
            transport_matches = transport_keywords & chunk_words
            if transport_matches:
                score += len(transport_matches) * 0.8

            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 1.0
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 1.0

            # 5. Penalty for very generic matches
            generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
            if query_words.issubset(generic_terms):
                score *= 0.1  # Heavy penalty for generic queries

            # Only include chunks with meaningful relevance (increased threshold)
            if score >= 0.8:  # Much higher threshold
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"Text-based website search found {len(result)} relevant chunks (threshold: 0.8)")

        return result

    except Exception as e:
        logger.error(f"Error in text-based website search: {str(e)}")
        return []


def search_documents_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Simple wrapper function for document search that combines multiple search strategies.
    This function is used by the query endpoint for document search.
    """
    try:
        logger.info(f"Searching documents in Supabase for: '{query}' (limit: {limit})")

        # Try multiple search strategies in order of preference

        # 1. First try content-based search (works well for specific queries)
        content_results = search_documents_by_content(query, limit)
        if content_results:
            logger.info(f"Content search found {len(content_results)} results")
            return content_results

        # 2. Try vector search if available
        try:
            from llm_router import generate_embedding
            query_embedding = generate_embedding(query)
            if query_embedding:
                vector_results = search_supabase_document_chunks_enhanced(
                    query_embedding=query_embedding,
                    query_text=query,
                    use_hybrid_search=True,
                    top_k=limit,
                    min_threshold=0.1
                )
                if vector_results:
                    logger.info(f"Vector search found {len(vector_results)} results")
                    return vector_results
        except Exception as e:
            logger.warning(f"Vector search failed: {str(e)}")

        # 3. Try text-based search as fallback
        text_results = text_based_document_search(query, limit)
        if text_results:
            logger.info(f"Text-based search found {len(text_results)} results")
            return text_results

        logger.info("No document results found with any search method")
        return []

    except Exception as e:
        logger.error(f"Error in search_documents_in_supabase: {str(e)}")
        return []


# Configure LLM models through the router
available_models = llm_router.get_available_models()
logger.info(f"Available LLM models: {[model['id'] for model in available_models]}")

# For backward compatibility
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.warning("GEMINI_API_KEY not found in environment variables. Using mock embeddings.")

# Global variable to store document chunks with embeddings
DOCUMENT_CHUNKS = []
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
# Threshold for considering document chunks relevant
RELEVANCE_THRESHOLD = 0.4  # ✅ Proper threshold for meaningful relevance (0.4 as per requirements)

# Create FastAPI app
app = FastAPI(title="Document Management System")

# Initialize database optimizer
db_optimizer = None
if DATABASE_OPTIMIZATION_AVAILABLE:
    try:
        db_optimizer = DatabaseOptimizer(supabase)
        logger.info("Database optimizer initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize database optimizer: {e}")

# Initialize memory manager
if MEMORY_MANAGEMENT_AVAILABLE:
    try:
        memory_manager.start()
        # Optimize for large datasets if we expect 10K+ documents
        memory_manager.optimize_for_large_dataset()
        logger.info("Memory manager initialized and optimized for large datasets")
    except Exception as e:
        logger.warning(f"Failed to initialize memory manager: {e}")

# Initialize performance manager
performance_manager = None
if PERFORMANCE_OPTIMIZATION_AVAILABLE:
    try:
        performance_manager = PerformanceManager(supabase)
        logger.info("Performance manager initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize performance manager: {e}")

# Initialize health check system
if HEALTH_CHECK_AVAILABLE:
    try:
        # Add health checks
        health_manager.add_check(DatabaseHealthCheck(supabase))
        health_manager.add_check(MemoryHealthCheck())
        health_manager.add_check(DiskHealthCheck())
        health_manager.add_check(VectorSearchHealthCheck(supabase))

        logger.info("Health check system initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize health check system: {e}")

# Import and include category management router
try:
    from category_management import router as category_router
    app.include_router(category_router)
    logger.info("Category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import category management router: {e}")
except Exception as e:
    logger.warning(f"Error including category management router: {e}")

# Import and include enhanced category management router
try:
    from enhanced_category_management import router as enhanced_category_router
    app.include_router(enhanced_category_router, prefix="/api/enhanced")
    logger.info("Enhanced category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import enhanced category management router: {e}")
except Exception as e:
    logger.warning(f"Error including enhanced category management router: {e}")

# Configure CORS for local development and production
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",  # Alternative localhost
        "https://railchatbot-cb555.web.app",  # Production domain
        "*"  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Custom middleware to increase file upload size limit
@app.middleware("http")
async def add_custom_upload_limit(request: Request, call_next):
    # Set a large upload limit (200MB)
    # This is handled at the application level, not the server level
    # For production, you should also configure your ASGI server (Uvicorn) with appropriate limits
    request.scope.setdefault("_body_size_limit", 200 * 1024 * 1024)  # 200MB
    response = await call_next(request)
    return response

# Define API request and response models
class QueryRequest(BaseModel):
    query: str
    model: Optional[str] = "gemini-2.0-flash"  # Changed to gemini-1.5-flash to fix timeout issues
    fallback_enabled: Optional[bool] = True  # Enable LLM fallback by default
    extract_format: Optional[str] = "paragraph"  # paragraph, bullet, or table
    use_hybrid_search: Optional[bool] = True  # Enable hybrid search by default
    retry_on_timeout: Optional[bool] = True  # Try fallback model on timeout
    context_mode: Optional[str] = "flexible"  # strict, flexible, or none (no context)

class WebsiteScrapeRequest(BaseModel):
    url: str

class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None
    follow_links: Optional[bool] = False
    extraction_depth: Optional[int] = 1
    extract_images: Optional[bool] = False
    extract_tables: Optional[bool] = True
    max_pages: Optional[int] = 10
    extractor_type: Optional[str] = "trafilatura"
    domain_category: Optional[str] = "general"
    embedding_model: Optional[str] = "gemini-2.0-flash"  # Model for embeddings
    # 4-level hierarchical category fields (UUIDs)
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    # Legacy fields for backward compatibility
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class Source(BaseModel):
    source_type: str  # "document" or "website"
    # For documents
    filename: Optional[str] = None
    name: Optional[str] = None  # Display name for the document
    page: Optional[int] = None
    pages: Optional[List[int]] = None  # aggregated pages for a document
    # For websites
    url: Optional[str] = None
    link: Optional[str] = None  # For document viewer links
    # For visual content
    content_type: Optional[str] = None  # "text", "table", "image", "chart_diagram"
    visual_content: Optional[Dict[str, Any]] = None  # Visual content metadata
    storage_url: Optional[str] = None  # URL for stored visual content
    display_type: Optional[str] = None  # "text", "html_table", "image", "base64_image"

class QueryResponse(BaseModel):
    answer: str  # Combined answer from all sources
    document_answer: Optional[str] = None  # Answer only from document sources
    website_answer: Optional[str] = None  # Answer only from website sources
    sources: List[Source]  # All sources
    document_sources: Optional[List[Source]] = None  # Only document sources
    website_sources: Optional[List[Source]] = None  # Only website sources
    llm_model: Optional[str] = None  # The LLM model used for generating the answer
    # Keep both the new and legacy flag names in the response for backward compatibility
    llm_fallback: Optional[bool] = False  # Legacy field expected by older front-end code
    llm_fallback_used: Optional[bool] = False  # CORRECTED: Whether the answer was generated using LLM fallback
    visual_content_found: Optional[bool] = False  # Whether visual content was found
    visual_content_types: Optional[List[str]] = None  # Types of visual content found

class ChunkData(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str
    embedding: Optional[List[float]] = None

# Document processing functions
def clean_text(text: str) -> str:
    """Clean text for processing."""
    return " ".join(text.split()) if text else ""

def detect_visual_query(query: str) -> Dict[str, Any]:
    """
    Detect if query is looking for visual content and what types.
    Enhanced to detect specific logos, brands, and companies.
    
    Returns:
        Dict with flags for different visual content types and specific entities
    """
    query_lower = query.lower()
    
    # Visual-related keywords
    table_keywords = ["table", "chart", "data", "specifications", "schedule", "list", "grid", "column", "row", "quotation"]
    image_keywords = ["image", "picture", "diagram", "figure", "illustration", "photo", "schematic", "project", "logo", "emblem", "symbol", "icon", "graphic", "drawing", "sketch", "map", "layout", "design", "blueprint", "plan", "screenshot", "snapshot", "visual", "artwork"]
    chart_keywords = ["chart", "graph", "diagram", "flowchart", "flow chart", "technical drawing", "blueprint"]
    
    # Logo and brand related keywords
    logo_keywords = ["logo", "emblem", "symbol", "icon", "brand", "company logo", "trademark", "wordmark", "letterhead"]
    
    # General visual keywords
    visual_keywords = ["visual", "show me", "display", "view", "picture", "graphic"]
    
    is_table_query = any(keyword in query_lower for keyword in table_keywords)
    is_image_query = any(keyword in query_lower for keyword in image_keywords)
    is_chart_query = any(keyword in query_lower for keyword in chart_keywords)
    is_logo_query = any(keyword in query_lower for keyword in logo_keywords)
    is_visual_query = any(keyword in query_lower for keyword in visual_keywords) or is_table_query or is_image_query or is_chart_query or is_logo_query
    
    # Extract specific entities (like "Project 1", "Quotation 2", etc.)
    specific_entities = []
    company_entities = []
    import re
    
    # Look for patterns like "Project 1", "Quotation 1", "Table 2", etc.
    entity_patterns = [
        r'project\s*(\d+)',
        r'quotation\s*(\d+)',
        r'table\s*(\d+)',
        r'image\s*(\d+)',
        r'figure\s*(\d+)',
    ]
    
    for pattern in entity_patterns:
        matches = re.findall(pattern, query_lower)
        if matches:
            entity_type = pattern.split('\\')[0]  # Get the entity type (project, quotation, etc.)
            for match in matches:
                specific_entities.append({
                    "type": entity_type,
                    "number": match,
                    "query_text": f"{entity_type} {match}"
                })
    
    # Extract company/brand names for logo queries
    if is_logo_query or "logo" in query_lower:
        # Common patterns for company names in logo queries
        company_patterns = [
            r'(?:logo\s+(?:of\s+)?|show\s+me\s+(?:the\s+)?logo\s+(?:of\s+)?)([A-Z][A-Za-z\s&]+?)(?:\s+logo|\s+enterprises|\s+company|\s+corp|\s+ltd|\s+inc|\s*$)',
            r'([A-Z][A-Za-z\s&]+?)\s+(?:enterprises|company|corp|ltd|inc)\s+logo',
            r'([A-Z][A-Za-z\s&]+?)\s+logo',
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                company_name = match.strip()
                if len(company_name) > 1:  # Avoid single characters
                    company_entities.append({
                        "type": "company",
                        "name": company_name,
                        "query_text": f"{company_name} logo"
                    })
    
    # Priority content types based on query analysis
    priority_content_types = []
    if is_logo_query:
        priority_content_types.append("logo")
    if is_image_query:
        priority_content_types.append("image")
    if is_table_query:
        priority_content_types.append("table")
    if is_chart_query:
        priority_content_types.append("chart_diagram")
    
    return {
        "is_visual": is_visual_query,
        "wants_tables": is_table_query,
        "wants_images": is_image_query,
        "wants_charts": is_chart_query,
        "wants_logos": is_logo_query,
        "specific_entities": specific_entities,
        "company_entities": company_entities,
        "priority_content_types": priority_content_types,
        "search_terms": [entity["query_text"] for entity in specific_entities + company_entities]
    }

def search_supabase_document_chunks_enhanced(query_embedding, query_text=None, use_hybrid_search=True,
                                           top_k=30, min_threshold=0.4, document_filter=None,
                                           visual_query_info=None):
    """
    Enhanced document search that prioritizes visual content when needed.
    STRICT RAILGPT IMPLEMENTATION: Uses 0.4 threshold for relevance filtering.
    Only returns chunks that meet the strict similarity requirement.
    """
    try:
        logger.info(f"Enhanced search - Visual query info: {visual_query_info}")
        
        # Quick check for logo queries - simplify the logic to prevent timeouts
        if visual_query_info and visual_query_info.get("wants_logos"):
            logger.info("🎯 Logo query detected - searching for visual content")
            
            # For logo queries, use a simple approach that doesn't timeout
            # Search all chunks with a focus on image content and company entities
            company_entities = visual_query_info.get("company_entities", [])
            
            if company_entities:
                # Extract company names for searching
                company_names = [entity.get("name", "").lower() for entity in company_entities]
                logger.info(f"Searching for company logos: {company_names}")
                
                # Use regular search but filter for visual content afterwards
                regular_chunks = search_supabase_document_chunks(
                    query_embedding=query_embedding,
                    query_text=query_text,
                    use_hybrid_search=use_hybrid_search,
                    top_k=top_k,
                    min_threshold=min_threshold
                )
                
                # Filter and boost chunks that contain company names or are images
                enhanced_chunks = []
                for chunk in regular_chunks:
                    chunk_text = chunk.get("text", "").lower()
                    metadata = chunk.get("metadata", {})
                    content_type = metadata.get("content_type", "text")
                    
                    # Check for company name matches
                    score_boost = 0.0
                    for company_name in company_names:
                        if company_name in chunk_text:
                            score_boost += 0.3
                            logger.info(f"Found company '{company_name}' in chunk")
                    
                    # Boost image content for logo queries
                    if content_type == "image":
                        score_boost += 0.4
                        logger.info(f"Found image content - boosting for logo query")
                    
                    # Apply boost and add to results
                    if score_boost > 0:
                        chunk["similarity"] = chunk.get("similarity", 0) + score_boost
                        enhanced_chunks.append(chunk)
                    else:
                        enhanced_chunks.append(chunk)
                
                # Sort by boosted similarity
                enhanced_chunks.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                logger.info(f"Enhanced search returned {len(enhanced_chunks)} chunks with logo boosting")
                return enhanced_chunks
        
        # For non-visual queries or if visual search fails, use regular search
        logger.info("Using regular search (non-visual query or fallback)")
        return search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query_text,
            use_hybrid_search=use_hybrid_search,
            top_k=top_k,
            min_threshold=min_threshold
        )
            
    except Exception as e:
        logger.error(f"Error in enhanced document search: {str(e)}")
        # Fallback to regular search
        return search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query_text,
            use_hybrid_search=use_hybrid_search,
            top_k=top_k,
            min_threshold=min_threshold
        )

def generate_embedding(text: str, model_id: str = "gemini-2.0-flash") -> List[float]:
    """Generate embedding vector for text using the LLM router."""
    try:
        # Use the LLM router to generate embeddings
        return llm_router.generate_embedding(text, model_id)
    except Exception as e:
        logger.error(f"Error generating embedding with {model_id}: {str(e)}")
        # Try with a different model before falling back to random
        try:
            logger.info(f"Retrying embedding generation with default model")
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Fallback to a consistent embedding rather than random
            # This ensures that if we use this fallback, at least all fallbacks will be similar
            # Using a seed ensures consistency
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def cosine_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings, handling string conversions.
    """
    # Convert embeddings to numpy arrays if they are not already
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0

        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0

        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error

def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4, document_filter=None):
    """
    Vector search for document chunks using proper pgvector syntax.
    Uses configurable similarity threshold for meaningful relevance.
    """
    try:
        logger.info(f"Vector search for documents: threshold={min_threshold}, top_k={top_k}")

        # Strategy 1: Try direct content search first (most reliable)
        if query_text:
            try:
                direct_results = search_documents_by_content(query_text, limit=top_k)
                if direct_results:
                    logger.info(f"Direct content search found {len(direct_results)} document chunks")
                    
                    # Convert to expected format with very lenient similarity calculation
                    result_chunks = []
                    for chunk in direct_results:
                        chunk_copy = dict(chunk)
                        # Calculate actual similarity based on text matching - ULTRA LENIENT
                        chunk_text = chunk_copy.get('text', '').lower()
                        query_lower = query_text.lower()

                        # Proper relevance scoring for meaningful results
                        query_words = set(query_lower.split())
                        chunk_words = set(chunk_text.split())
                        common_words = query_words.intersection(chunk_words)

                        # Base similarity calculation
                        if len(query_words) > 0:
                            word_match_ratio = len(common_words) / len(query_words)
                        else:
                            word_match_ratio = 0.0

                        # Start with proper base similarity
                        similarity = word_match_ratio * 0.5  # Proper scoring for meaningful relevance

                        # Boost for exact phrase matches
                        if query_lower in chunk_text:
                            similarity += 0.3

                        # Boost for partial phrase matches
                        query_phrases = [phrase.strip() for phrase in query_lower.split() if len(phrase.strip()) > 1]
                        for phrase in query_phrases:
                            if phrase in chunk_text and len(phrase) > 1:
                                similarity += 0.1

                        # Apply minimum threshold for meaningful relevance
                        if len(common_words) > 0 and similarity >= min_threshold:
                            similarity = max(similarity, min_threshold)

                        # Cap similarity at 0.95
                        similarity = min(0.95, similarity)

                        chunk_copy['similarity'] = similarity
                        chunk_copy['source_type'] = 'document'
                        
                        # Ensure required fields are present
                        if 'page' not in chunk_copy and 'page_number' in chunk_copy:
                            chunk_copy['page'] = chunk_copy['page_number']
                        
                        # Extract or set filename
                        if not chunk_copy.get('filename'):
                            text = chunk_copy.get('text', '')
                            import re
                            match = re.search(r'Document:\s*([^,]+)', text)
                            if match:
                                filename = match.group(1).strip()
                                if not filename.endswith(('.pdf', '.docx', '.txt')):
                                    filename += '.pdf'
                                chunk_copy['filename'] = filename
                            else:
                                chunk_copy['filename'] = 'Unknown document'
                        
                        result_chunks.append(chunk_copy)
                    
                    return result_chunks[:top_k]
                        
            except Exception as e:
                logger.warning(f"Direct content search failed: {str(e)}")

        # Strategy 2: Try RPC function (if it works)
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search (skip if known to fail)
            result = supabase.rpc(
                'search_document_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"✅ RPC search found {len(result.data)} document chunks")

                # Convert to expected format with comprehensive field mapping
                result_chunks = []
                for chunk in result.data:
                    chunk_copy = dict(chunk)
                    chunk_copy['source_type'] = 'document'

                    # Ensure required fields are present with fallbacks
                    if 'page' not in chunk_copy and 'page_number' in chunk_copy:
                        chunk_copy['page'] = chunk_copy['page_number']
                    elif 'page' not in chunk_copy:
                        chunk_copy['page'] = 1

                    # Ensure filename is present
                    if not chunk_copy.get('filename'):
                        chunk_copy['filename'] = 'Unknown Document'

                    # Ensure similarity is present
                    if 'similarity' not in chunk_copy:
                        chunk_copy['similarity'] = min_threshold

                    result_chunks.append(chunk_copy)

                logger.info(f"✅ Processed {len(result_chunks)} document chunks from RPC")
                return result_chunks
            else:
                logger.info("RPC search returned no data")

        except Exception as e:
            logger.warning(f"RPC search failed: {str(e)}")
            # Continue to fallback methods

        # Fallback to local search with proper embeddings
        logger.info("Using local vector search with cosine similarity")
        global DOCUMENT_CHUNKS

        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]
        
        # Ensure document metadata is properly attached to all chunks
        # This prevents "Unknown document" issues in source attribution
        try:
            # Get document IDs from chunks
            doc_ids = [chunk.get('document_id') for chunk in result if chunk.get('document_id')]
            if doc_ids:
                # Query documents table to get filenames and other metadata
                from supabase_client import supabase
                query = f"""
                SELECT id, COALESCE(display_name, file_name, name) as filename, file_path, name
                FROM documents
                WHERE id IN ({','.join([f"'{id}'" for id in doc_ids])})
                """
                doc_results = supabase.execute_query(query)
                if doc_results and len(doc_results) > 0:
                    # Create lookup dictionary
                    doc_lookup = {doc['id']: doc for doc in doc_results}
                    # Update all chunks with their document metadata
                    for chunk in result:
                        doc_id = chunk.get('document_id')
                        if doc_id and doc_id in doc_lookup:
                            chunk['filename'] = doc_lookup[doc_id]['filename']
                            chunk['url'] = doc_lookup[doc_id]['file_path'] if 'file_path' in doc_lookup[doc_id] else None
                            logger.info(f"Enhanced chunk metadata with filename: {chunk['filename']}")
        except Exception as e:
            logger.error(f"Error enhancing chunks with document metadata: {str(e)}")

        logger.info(f"Local vector search found {len(result)} document chunks with similarity >= {min_threshold}")
        if result:
            similarities = [f"{c.get('similarity', 0):.3f}" for c in result[:3]]
            logger.info(f"Top similarities: {similarities}")

        return result

    except Exception as e:
        logger.error(f"Error in vector document search: {str(e)}")
        return []

def search_supabase_website_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4, website_filter=None):
    """
    Vector search for website chunks using Supabase pgvector.
    Uses configurable similarity threshold for meaningful relevance.
    """
    try:
        logger.info(f"Vector search for websites: threshold={min_threshold}, top_k={top_k}")

        # First try to use Supabase RPC function with low threshold
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search if available
            result = supabase.rpc(
                'search_website_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                logger.info(f"✅ RPC website search found {len(result.data)} chunks with threshold {min_threshold}")

                # Convert to expected format with comprehensive field mapping
                result_chunks = []
                for chunk in result.data:
                    chunk_copy = dict(chunk)
                    chunk_copy['source_type'] = 'website'

                    # Ensure required fields are present with fallbacks
                    if not chunk_copy.get('url'):
                        chunk_copy['url'] = 'Unknown URL'

                    # Ensure similarity is present
                    if 'similarity' not in chunk_copy:
                        chunk_copy['similarity'] = min_threshold

                    result_chunks.append(chunk_copy)

                logger.info(f"✅ Processed {len(result_chunks)} website chunks from RPC")
                return result_chunks
            else:
                logger.info("RPC website search returned no data")

        except Exception as rpc_error:
            logger.warning(f"RPC website search failed: {str(rpc_error)}, trying direct SQL")

        # Fallback to direct SQL query with very low threshold
        try:
            # Convert embedding to proper format for pgvector
            embedding_str = str(query_embedding).replace(' ', '')

            # Use direct SQL with pgvector cosine similarity - VERY LOW THRESHOLD
            query = f"""
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                w.url,
                w.title as website_name,
                wc.metadata,
                1 - (wc.embedding <=> '{embedding_str}') as similarity
            FROM website_chunks wc
            JOIN websites w ON wc.website_id = w.id
            WHERE 1 - (wc.embedding <=> '{embedding_str}') > {min_threshold}
            ORDER BY wc.embedding <=> '{embedding_str}'
            LIMIT {top_k};
            """

            result = supabase.execute_query(query)
        except Exception as sql_error:
            logger.error(f"Direct SQL website search failed: {str(sql_error)}")
            # Try even more lenient search with text-based fallback
            try:
                logger.info("Trying text-based website search as final fallback")
                return text_based_website_search(query_text or "", top_k=top_k)
            except:
                return []

        if not result or not isinstance(result, list):
            logger.info("No website chunks found with vector search, trying text-based search")
            # Try text-based search as fallback
            try:
                return text_based_website_search(query_text or "", top_k=top_k)
            except:
                return []

        # Add source_type to each chunk
        for chunk in result:
            chunk["source_type"] = "website"
            # Ensure minimum similarity for very low threshold results
            if chunk.get("similarity", 0) < min_threshold:
                chunk["similarity"] = min_threshold

        logger.info(f"Found {len(result)} website chunks with low threshold vector similarity")
        return result

    except Exception as e:
        logger.error(f"Error in vector website search: {str(e)}")
        # Final fallback to any available website chunks
        try:
            logger.info("Final fallback: trying text-based website search")
            return text_based_website_search(query_text or "", top_k=top_k)
        except:
            return []

def group_chunks_by_source(chunks):
    """
    Group chunks by source type into document and website categories.

    Args:
        chunks: List of chunks with source_type information

    Returns:
        Tuple of (document_chunks, website_chunks)
    """
    document_chunks = []
    website_chunks = []

    for chunk in chunks:
        source_type = chunk.get("source_type", "").lower()
        if source_type == "document":
            document_chunks.append(chunk)
        elif source_type == "website":
            website_chunks.append(chunk)

    return document_chunks, website_chunks

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a coherent answer using the LLM router based on similar chunks.

    Args:
        query: The user's question
        similar_chunks: List of context chunks to use for generating the answer
        system_prompt: Custom system prompt to use (optional)
        model_id: LLM model to use (default: gemini-2.0-flash)
        extract_format: Preferred format for the extraction (paragraph, bullet, table)

    Returns:
        Tuple of (answer_text, source_data, document_sources, website_sources)
    """
    try:
        # Prepare context from similar chunks
        context_texts = []
        sources = []
        document_sources = []
        website_sources = []

        # Ensure all chunks have valid text
        valid_chunks = []
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str) or not chunk["text"].strip():
                # Log the issue and try to fix it
                logger.warning(f"Found chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                # Try to fix it with a default value
                chunk["text"] = chunk.get("text", "") or "No content available"

            # Ensure source_type is set - use a more aggressive approach to reduce warnings
            if "source_type" not in chunk or not chunk["source_type"]:
                if "filename" in chunk or "page" in chunk or "document_id" in chunk:
                    chunk["source_type"] = "document"
                    # Use less verbose logging to avoid flooding logs
                    if random.random() < 0.01:  # Only log 1% of these to reduce noise
                        logger.debug(f"Set source_type to 'document' for chunk {chunk.get('id', 'unknown')}")
                elif "url" in chunk or "website_id" in chunk:
                    chunk["source_type"] = "website"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'website' for chunk {chunk.get('id', 'unknown')}")
                else:
                    chunk["source_type"] = "unknown"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'unknown' for chunk {chunk.get('id', 'unknown')}")

            valid_chunks.append(chunk)

        # Use only valid chunks
        similar_chunks = valid_chunks

        # If no valid chunks, return early
        if not similar_chunks:
            logger.warning("No valid chunks found for LLM answer generation")
            return "I couldn't find any valid information to answer your question.", [], [], []

        # Log the content of chunks to help diagnose issues
        logger.info(f"Processing {len(similar_chunks)} chunks for answer generation")
        for i, chunk in enumerate(similar_chunks[:3]):  # Log first 3 chunks for debugging
            logger.info(f"Chunk {i}: source_type={chunk.get('source_type', 'unknown')}, similarity={chunk.get('similarity', 0):.3f}, text_length={len(chunk.get('text', ''))}, text_preview={chunk.get('text', '')[:100]}...")

        for chunk in similar_chunks:
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            # Add context with citation information
            if chunk.get("source_type") == "document":
                # Document source
                filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                page = chunk.get("page", 1)
                context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "document",
                    "filename": filename,
                    "page": page,
                    # For UI display
                    "name": os.path.basename(filename),
                    "link": f"/viewer?file={filename}&page={page}"
                }
                if source not in sources:
                    sources.append(source)
                if source not in document_sources:
                    document_sources.append(source)
            elif chunk.get("source_type") == "website":
                # Website source - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "website",
                    "url": url
                }
                if source not in sources:
                    sources.append(source)
                if source not in website_sources:
                    website_sources.append(source)
            else:
                # Unknown source type
                logger.warning(f"Unknown source type for chunk: {chunk.get('id', 'unknown')}")
                context_texts.append(f"From unknown source (relevance: {similarity:.2f}):\n{chunk_text}\n")

        # Combine all context texts
        context = "\n\n".join(context_texts)
        logger.info(f"Created context with {len(context_texts)} context segments, total length: {len(context)} characters")

        # Token limit handling - approximately 4 characters per token for English text
        # 1M tokens is the absolute limit for Gemini, so we'll stay well under that
        MAX_MODEL_TOKENS = 1000000  # Gemini's maximum token limit
        SAFETY_FACTOR = 0.25  # Use only 75% of the maximum to leave room for response
        CHARS_PER_TOKEN = 4  # Approximate chars per token in English text

        max_context_chars = int((MAX_MODEL_TOKENS * SAFETY_FACTOR) * CHARS_PER_TOKEN)
        logger.info(f"Maximum context size: {max_context_chars} chars (~{int(MAX_MODEL_TOKENS * SAFETY_FACTOR)} tokens)")

        if len(context) > max_context_chars:
            logger.warning(f"Context too large ({len(context)} chars / ~{int(len(context)/CHARS_PER_TOKEN)} tokens), truncating to ~{max_context_chars} chars")

            # Sort chunks by similarity before truncating
            sorted_chunks = sorted(similar_chunks, key=lambda x: x.get('similarity', 0), reverse=True)

            # Start with highest similarity chunks
            new_context_texts = []
            new_context_length = 0
            preserved_sources = []
            preserved_doc_sources = []
            preserved_web_sources = []

            # First include top 5 highest similarity chunks regardless of size (to ensure best matches are included)
            top_chunks = sorted_chunks[:5]
            remaining_chunks = sorted_chunks[5:]

            # Add top chunks first
            for chunk in top_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue

                # Truncate extremely large chunks to a reasonable size
                if len(chunk.get("text", "")) > 10000:  # If chunk is > 10K chars
                    chunk["text"] = chunk["text"][:10000] + "...[truncated due to length]"
                    logger.info(f"Truncated very large chunk of {len(chunk.get('text', ''))} chars")

                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"
                new_context_texts.append(chunk_text)
                new_context_length += len(chunk_text)

                # Track which sources we're keeping
                if chunk.get("source_type") == "document":
                    source = {
                        "source_type": "document",
                        "filename": chunk.get("filename", "Unknown document"),
                        "page": chunk.get("page", 1),
                        "name": os.path.basename(chunk.get("filename", "Unknown")),
                        "link": f"/viewer?file={chunk.get('filename', 'Unknown')}&page={chunk.get('page', 1)}"
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_doc_sources:
                        preserved_doc_sources.append(source)
                elif chunk.get("source_type") == "website":
                    source = {
                        "source_type": "website",
                        "url": chunk.get("url", "Unknown website")
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_web_sources:
                        preserved_web_sources.append(source)

            # Now add remaining chunks up to the limit
            for chunk in remaining_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue

                # Truncate very large chunks
                if len(chunk.get("text", "")) > 5000:  # More aggressive truncation for non-top chunks
                    chunk["text"] = chunk["text"][:5000] + "...[truncated due to length]"

                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"

                # Check if adding this chunk would exceed our limit
                if new_context_length + len(chunk_text) < max_context_chars:
                    new_context_texts.append(chunk_text)
                    new_context_length += len(chunk_text)

                    # Track which sources we're keeping
                    if chunk.get("source_type") == "document":
                        source = {
                            "source_type": "document",
                            "filename": chunk.get("filename", "Unknown document"),
                            "page": chunk.get("page", 1),
                            "name": os.path.basename(chunk.get("filename", "Unknown")),
                            "link": f"/viewer?file={chunk.get('filename', 'Unknown')}&page={chunk.get('page', 1)}"
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_doc_sources:
                            preserved_doc_sources.append(source)
                    elif chunk.get("source_type") == "website":
                        source = {
                            "source_type": "website",
                            "url": chunk.get("url", "Unknown website")
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_web_sources:
                            preserved_web_sources.append(source)
                else:
                    # We've reached our limit
                    break

            # Replace the original collections with our trimmed versions
            context = "\n\n".join(new_context_texts)
            sources = preserved_sources
            document_sources = preserved_doc_sources
            website_sources = preserved_web_sources

            logger.info(f"Truncated context to {len(context)} chars (~{int(len(context)/CHARS_PER_TOKEN)} tokens), preserving {len(new_context_texts)} highest relevance chunks")

        if not context.strip():
            logger.warning("Combined context is empty after processing chunks")
            return "I don't have information to answer that question.", [], [], []

        # Default system prompt if none provided
        if not system_prompt:
            system_prompt = f"""
You are RailGPT, an expert information retrieval assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. You MUST use the information provided in the context below to answer the question.
2. The context contains relevant railway information that was specifically retrieved for this query.
3. ALWAYS provide an answer using the context information, even if it seems incomplete.
4. Include source references for information you provide (document names, pages, website URLs).
5. If the context has limited information, start with "Based on the available railway information:" and then provide what you can from the context.
6. DO NOT say "I couldn't find any valid information" unless the context is truly empty.
7. Format your response clearly with proper paragraphs.

Remember: The context below was specifically selected as relevant to the user's query, so use it to provide a helpful answer.

CONTEXT:
{context}
"""

            # Additional instructions
            system_prompt += """
NEVER use your general knowledge to answer the question.
ONLY use the information provided in the context.
If the context doesn't contain enough information, say "Based on the available document information..." and then answer with what you can find in the context.
If you're unsure about the answer based on the context, state what you can determine from the context and indicate that the information may be limited.
"""

        # Add format preference to system prompt
        if extract_format == "bullet":
            system_prompt += "\nStructure your answer using bullet points where appropriate for better readability."
        elif extract_format == "table":
            system_prompt += "\nIf the answer contains tabular data, format it as a markdown table for better readability."
        else: # paragraph (default)
            system_prompt += "\nStructure your answer in clear paragraphs for better readability."

        # Use the LLM router to generate the answer
        try:
            # Generate the answer with the LLM
            answer = llm_router.generate_answer(query, context, system_prompt, model_id)

            # Process the results based on the source types
            # If there are only document sources, only return document answer
            if document_sources and not website_sources:
                return answer, sources, document_sources, []
            # If there are only website sources, only return website answer
            elif website_sources and not document_sources:
                return answer, sources, [], website_sources
            # If there are both document and website sources, or no sources
            else:
                return answer, sources, document_sources, website_sources

        except Exception as e:
            logger.error(f"Error generating LLM answer: {str(e)}")
            # Fallback to a simpler prompt
            try:
                fallback_answer = llm_router.generate_answer(
                    query=query,
                    context="",  # Empty context for fallback
                    system_prompt="You are an AI assistant. Answer this question to the best of your ability: " + query,
                    model_id=model_id
                )
                return fallback_answer, [], [], []
            except Exception as fallback_err:
                logger.error(f"Fallback also failed: {str(fallback_err)}")
                return "I'm sorry, I couldn't process your question. Please try again.", [], [], []

    except Exception as e:
        # Catch-all for any other errors
        logger.error(f"Unexpected error in generate_llm_answer with {model_id}: {str(e)}")
        return f"I encountered an error while trying to generate an answer: {str(e)}", [], [], []

def load_documents(data_dir: str = './data'):
    """Load and process all supported documents from a directory."""
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []

    logger.info(f"Loading documents on startup")

    # Load document chunks from Supabase first using the correct method
    try:
        from supabase_client import supabase
        logger.info("Loading all document chunks from Supabase")

        # Use the table() method for better compatibility
        chunks_query = "SELECT * FROM document_chunks"
        chunks_result = supabase.execute_query(chunks_query)
        
        logger.info(f"Loaded {len(chunks_result) if chunks_result else 0} document chunks from Supabase")
        
        # Load document chunks
        if chunks_result and len(chunks_result) > 0:
            for chunk in chunks_result:
                # Get document info for each chunk
                try:
                    doc_query = f"SELECT display_name, file_path FROM documents WHERE id = '{chunk.get('document_id')}'"
                    doc_result = supabase.execute_query(doc_query)
                    
                    if doc_result and len(doc_result) > 0:
                        doc_info = doc_result[0]
                        chunk['filename'] = doc_info.get('display_name', 'Unknown')
                        chunk['url'] = doc_info.get('file_path', '')
                    else:
                        chunk['filename'] = 'Unknown Document'
                        chunk['url'] = ''
                    
                    chunk['source_type'] = 'document'
                    chunk['similarity'] = 0.85
                    
                    # Add embedding if needed
                    if "embedding" not in chunk or not chunk["embedding"]:
                        chunk_text = chunk.get("text", "")
                        if chunk_text:
                            try:
                                chunk["embedding"] = generate_embedding(chunk_text)
                            except Exception as e:
                                logger.warning(f"Failed to generate embedding for chunk {chunk.get('id', 'unknown')}: {str(e)}")
                                chunk["embedding"] = [0.01] * 768
                        else:
                            chunk["embedding"] = [0.01] * 768
                    
                    DOCUMENT_CHUNKS.append(chunk)
                except Exception as e:
                    logger.warning(f"Error processing document chunk {chunk.get('id', 'unknown')}: {str(e)}")
                    
        logger.info(f"Loaded {len([c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'document'])} document chunks from Supabase")
    
    except Exception as e:
        logger.error(f"Error loading document chunks from Supabase: {str(e)}")

    # Also load website chunks from Supabase
    try:
        from supabase_client import supabase
        logger.info("Loading all website chunks from Supabase")

        # Use the table() method for better compatibility
        chunks_query = "SELECT * FROM website_chunks"
        chunks_result = supabase.execute_query(chunks_query)
        
        logger.info(f"Loaded {len(chunks_result) if chunks_result else 0} website chunks from Supabase")
        
        # Load website chunks
        if chunks_result and len(chunks_result) > 0:
            for chunk in chunks_result:
                # Get website info for each chunk
                try:
                    website_query = f"SELECT url, domain FROM websites WHERE id = '{chunk.get('website_id')}'"
                    website_result = supabase.execute_query(website_query)
                    
                    if website_result and len(website_result) > 0:
                        website_info = website_result[0]
                        chunk['url'] = website_info.get('url', 'Unknown URL')
                        chunk['domain'] = website_info.get('domain', 'Unknown Domain')
                    else:
                        chunk['url'] = 'Unknown Website'
                        chunk['domain'] = 'Unknown Domain'
                    
                    chunk['source_type'] = 'website'
                    chunk['similarity'] = 0.70
                    
                    # Add embedding if needed
                    if "embedding" not in chunk or not chunk["embedding"]:
                        chunk_text = chunk.get("text", "")
                        if chunk_text:
                            try:
                                chunk["embedding"] = generate_embedding(chunk_text)
                            except Exception as e:
                                logger.warning(f"Failed to generate embedding for website chunk {chunk.get('id', 'unknown')}: {str(e)}")
                                chunk["embedding"] = [0.01] * 768
                        else:
                            chunk["embedding"] = [0.01] * 768
                    
                    DOCUMENT_CHUNKS.append(chunk)
                except Exception as e:
                    logger.warning(f"Error processing website chunk {chunk.get('id', 'unknown')}: {str(e)}")
                    
        logger.info(f"Loaded {len([c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'website'])} website chunks from Supabase")
    
    except Exception as e:
        logger.error(f"Error loading website chunks from Supabase: {str(e)}")

    # Ensure data directory exists
    if not os.path.exists(data_dir):
        logger.warning(f"No supported document files found in {data_dir}")
        return DOCUMENT_CHUNKS

    # Get all supported document files in the directory
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    document_files = []

    for ext in supported_extensions:
        files = [f for f in os.listdir(data_dir) if f.lower().endswith(ext)]
        document_files.extend(files)

    if not document_files:
        logger.warning(f"No supported document files found in {data_dir}")

    return DOCUMENT_CHUNKS

@app.on_event("startup")
async def startup_event():
    """Load documents on server startup and check Supabase configuration."""
    logger.info("Loading documents on startup")
    load_documents()

    # Log how many chunks were loaded
    logger.info(f"Loaded {len(DOCUMENT_CHUNKS)} document chunks on startup")

    # CLEAR and reload chunks properly from Supabase database
    try:
        logger.info("Loading additional chunks from Supabase database...")
        
        # Clear existing chunks first to avoid duplicates
        DOCUMENT_CHUNKS.clear()
        
        # Load document chunks from database
        doc_chunks = supabase.table("document_chunks").select("*").execute()
        if doc_chunks.data:
            for chunk in doc_chunks.data:
                chunk['source_type'] = 'document'
                # Get filename from documents table if not present
                if not chunk.get('filename') and chunk.get('document_id'):
                    try:
                        doc_result = supabase.table("documents").select("display_name, file_path").eq("id", chunk['document_id']).execute()
                        if doc_result.data:
                            chunk['filename'] = doc_result.data[0].get('display_name', 'Unknown')
                            chunk['url'] = doc_result.data[0].get('file_path', '')
                        else:
                            chunk['filename'] = 'Unknown document'
                            chunk['url'] = ''
                    except:
                        chunk['filename'] = 'Unknown document'
                        chunk['url'] = ''
                        
            DOCUMENT_CHUNKS.extend(doc_chunks.data)
            logger.info(f"✅ Loaded {len(doc_chunks.data)} document chunks from database")
        else:
            logger.warning("No document chunks found in database")
        
        # Load website chunks from database
        web_chunks = supabase.table("website_chunks").select("*").execute()
        if web_chunks.data:
            for chunk in web_chunks.data:
                chunk['source_type'] = 'website'
                # Get website info
                if chunk.get('website_id'):
                    try:
                        website_result = supabase.table("websites").select("url, domain").eq("id", chunk['website_id']).execute()
                        if website_result.data:
                            chunk['url'] = website_result.data[0].get('url', 'Unknown URL')
                            chunk['domain'] = website_result.data[0].get('domain', 'Unknown Domain')
                    except:
                        chunk['url'] = 'Unknown URL'
                        chunk['domain'] = 'Unknown Domain'
                        
            DOCUMENT_CHUNKS.extend(web_chunks.data)
            logger.info(f"✅ Loaded {len(web_chunks.data)} website chunks from database")
        else:
            logger.warning("No website chunks found in database")
        
        logger.info(f"🎉 Total chunks now in memory: {len(DOCUMENT_CHUNKS)}")
        
    except Exception as e:
        logger.error(f"Error loading chunks from database: {str(e)}")

    # Check Supabase configuration
    try:
        # Check if Supabase is configured
        if os.getenv("USE_SUPABASE", "true").lower() == "true":
            # Test Supabase connection
            result = supabase.execute_query("SELECT 1 as test")
            if "error" in result:
                logger.error(f"Supabase connection test failed: {result['error']}")
            else:
                logger.info("Supabase connection test successful")

                # Skip schema checks and directly try to create/access buckets
                try:
                    logger.info("Checking Supabase storage setup...")

                    # Directly try to create the documents bucket
                    # This will either succeed or fail gracefully with a mock response
                    bucket_result = supabase.create_bucket_if_not_exists("documents")

                    if "error" in bucket_result:
                        logger.info(f"Using mock storage bucket for 'documents': {bucket_result.get('id')}")
                    else:
                        logger.info(f"Successfully accessed/created bucket: {bucket_result.get('id')}")

                    # Create additional buckets if needed
                    supabase.create_bucket_if_not_exists("websites", is_public=True)
                    supabase.create_bucket_if_not_exists("images", is_public=True)
                except Exception as e:
                    logger.warning(f"Error setting up Supabase storage: {str(e)}")
                    logger.info("Will use mock storage functionality when needed.")
    except Exception as e:
        logger.error(f"Error checking Supabase configuration: {str(e)}")

# Root endpoint
@app.get("/")
async def read_root():
    return {"message": "Document Management System API is running"}

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "API is running"}

# Environment variables check endpoint
@app.get("/api/env-check")
async def env_check():
    """Check environment variables for debugging"""
    import os
    return {
        "SUPABASE_URL": os.getenv("SUPABASE_URL", "NOT_SET"),
        "SUPABASE_KEY_SET": "YES" if os.getenv("SUPABASE_KEY") else "NO",
        "SUPABASE_ANON_KEY_SET": "YES" if os.getenv("SUPABASE_ANON_KEY") else "NO",
        "supabase_client_status": "initialized" if hasattr(supabase, 'supabase') and supabase.supabase else "not_initialized"
    }

# Alternative health check endpoint (for compatibility)
@app.get("/health")
async def health_check_alt():
    return {"status": "ok", "message": "API is running"}

@app.get("/api/debug/paths")
async def debug_paths():
    """Debug endpoint to check server paths"""
    import os

    cwd = os.getcwd()
    script_dir = os.path.dirname(os.path.abspath(__file__))

    test_files = ["SampleRailwayDoc.pdf", "ACP 110V.docx"]
    file_checks = {}

    for filename in test_files:
        possible_paths = [
            os.path.join(script_dir, "data", "uploads", filename),
            os.path.join(script_dir, "data", filename),
            f"./backend/data/uploads/{filename}",
            f"./data/uploads/{filename}",
            f"./uploads/{filename}",
        ]

        file_checks[filename] = []
        for path in possible_paths:
            file_checks[filename].append({
                "path": path,
                "exists": os.path.exists(path)
            })

    return {
        "current_working_directory": cwd,
        "script_directory": script_dir,
        "file_checks": file_checks
    }

@app.get("/api/test/view/{filename}")
async def test_view_document(filename: str):
    """Test endpoint to check if route matching works"""
    return {
        "message": f"Test endpoint reached with filename: {filename}",
        "decoded_filename": filename
    }

# Clear database endpoint (for testing only)
@app.post("/api/clear-database")
async def clear_database():
    """Clear all data from the database (for testing purposes)"""
    try:
        result = supabase.clear_all_data()
        return {"message": "Database cleared successfully", "result": result}
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing database: {str(e)}")

# Get all documents endpoint with pagination and optimization
@app.get("/api/documents")
async def get_documents(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("DESC", regex="^(ASC|DESC)$", description="Sort order"),
    search: Optional[str] = Query(None, description="Search term"),
    category: Optional[str] = Query(None, description="Filter by category"),
    status: Optional[str] = Query(None, description="Filter by status"),
    date_from: Optional[str] = Query(None, description="Filter from date"),
    date_to: Optional[str] = Query(None, description="Filter to date")
):
    """Get paginated documents from the database with filtering and sorting."""
    try:
        logger.info(f"Fetching documents - page: {page}, size: {page_size}, sort: {sort_by} {sort_order}")

        # Use optimized pagination if available
        if db_optimizer:
            params = PaginationParams(
                page=page,
                page_size=page_size,
                sort_by=sort_by,
                sort_order=sort_order,
                filters={
                    "search": search,
                    "category": category,
                    "status": status,
                    "date_from": date_from,
                    "date_to": date_to
                }
            )

            result = await db_optimizer.paginator.paginate_documents(params)

            # Process the results to match frontend expectations
            if result.get("data"):
                processed_documents = []
                for doc in result["data"]:
                    processed_doc = {
                        "id": doc.get("id", "unknown"),
                        "name": doc.get("display_name") or doc.get("file_name") or doc.get("name") or "Unknown Document",
                        "uploadedAt": doc.get("created_at", ""),
                        "fileType": doc.get("file_type", "unknown"),
                        "fileSize": doc.get("file_size", 0),
                        "filePath": doc.get("file_path", ""),
                        "uploadedBy": doc.get("uploaded_by", "unknown"),
                        "status": doc.get("status", "processed"),
                        "qualityScore": doc.get("extraction_quality", 85),
                        "mainCategory": doc.get("main_category", "Documents"),
                        "category": doc.get("category", "Uploaded"),
                        "originalData": doc
                    }
                    processed_documents.append(processed_doc)

                result["data"] = processed_documents

            logger.info(f"Optimized pagination returned {len(result.get('data', []))} documents")
            return result

        # Fallback to basic implementation
        logger.info("Using fallback document pagination")

        # Build query with filters
        query_parts = ["SELECT * FROM documents WHERE 1=1"]

        if search:
            query_parts.append(f"AND (display_name ILIKE '%{search}%' OR file_name ILIKE '%{search}%')")
        if category:
            query_parts.append(f"AND main_category = '{category}'")
        if status:
            query_parts.append(f"AND status = '{status}'")
        if date_from:
            query_parts.append(f"AND created_at >= '{date_from}'")
        if date_to:
            query_parts.append(f"AND created_at <= '{date_to}'")

        # Add sorting and pagination
        offset = (page - 1) * page_size
        query_parts.append(f"ORDER BY {sort_by} {sort_order}")
        query_parts.append(f"LIMIT {page_size} OFFSET {offset}")

        documents_query = " ".join(query_parts)
        result = supabase.execute_query(documents_query)

        # Get total count
        count_query = "SELECT COUNT(*) as total FROM documents WHERE 1=1"
        if search:
            count_query += f" AND (display_name ILIKE '%{search}%' OR file_name ILIKE '%{search}%')"
        if category:
            count_query += f" AND main_category = '{category}'"
        if status:
            count_query += f" AND status = '{status}'"

        count_result = supabase.execute_query(count_query)
        total_count = count_result[0]["total"] if count_result else 0

        # Process documents
        processed_documents = []
        if result:
            for doc in result:
                processed_doc = {
                    "id": doc.get("id", "unknown"),
                    "name": doc.get("display_name") or doc.get("file_name") or doc.get("name") or "Unknown Document",
                    "uploadedAt": doc.get("created_at", ""),
                    "fileType": doc.get("file_type", "unknown"),
                    "fileSize": doc.get("file_size", 0),
                    "filePath": doc.get("file_path", ""),
                    "uploadedBy": doc.get("uploaded_by", "unknown"),
                    "status": doc.get("status", "processed"),
                    "qualityScore": doc.get("extraction_quality", 85),
                    "mainCategory": doc.get("main_category", "Documents"),
                    "category": doc.get("category", "Uploaded"),
                    "originalData": doc
                }
                processed_documents.append(processed_doc)

        total_pages = (total_count + page_size - 1) // page_size

        return {
            "data": processed_documents,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }

    except Exception as e:
        logger.error(f"Error fetching documents from Supabase: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return {
            "data": [],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": 0,
                "total_pages": 0,
                "has_next": False,
                "has_prev": False
            }
        }

# Get all websites endpoint
@app.get("/api/websites")
async def get_websites():
    """Get all websites from Supabase database"""
    try:
        logger.info("Fetching websites from Supabase database")

        # First, let's see what columns are actually available in the websites table
        try:
            # Get table schema first
            schema_query = "SELECT * FROM websites LIMIT 1"
            schema_result = supabase.execute_query(schema_query)
            if schema_result:
                logger.info(f"Sample website record: {schema_result[0] if schema_result else 'No records'}")
        except Exception as e:
            logger.error(f"Error checking websites table schema: {str(e)}")

        # Query websites from Supabase with proper error handling
        websites_query = """
        SELECT * FROM websites
        ORDER BY created_at DESC
        """

        result = supabase.execute_query(websites_query)

        if result and len(result) > 0:
            # Process the results to match frontend expectations
            processed_websites = []
            for site in result:
                logger.info(f"Processing website: {site}")

                # Extract domain from URL
                url = site.get("url", "")
                try:
                    from urllib.parse import urlparse
                    domain = urlparse(url).netloc.replace('www.', '')
                except:
                    domain = url

                # Map Supabase fields to frontend expected fields
                processed_site = {
                    "id": site.get("id", "unknown"),
                    "name": site.get("name") or site.get("title") or domain,
                    "url": url,
                    "domain": domain,
                    "extractedAt": site.get("created_at", ""),
                    "status": "Success" if site.get("status", "processed") == "processed" else "Failed",
                    "submittedBy": site.get("submitted_by", "unknown"),
                    "domainCategory": site.get("category", "General"),
                    # Hierarchical category fields
                    "mainCategory": site.get("main_category"),
                    "category": site.get("category_level2"),
                    "subCategory": site.get("sub_category"),
                    "minorCategory": site.get("minor_category"),
                    # Additional fields for debugging
                    "originalData": site  # Keep original data for debugging
                }
                processed_websites.append(processed_site)

            logger.info(f"Found {len(processed_websites)} websites in database")
            return processed_websites
        else:
            logger.info("No websites found in database")
            return []

    except Exception as e:
        logger.error(f"Error fetching websites from Supabase: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty list on error instead of mock data
        return []


def format_visual_content_for_frontend(metadata, content_type):
    """Format visual content metadata for frontend consumption."""
    if not metadata or content_type == "text":
        return None
    
    formatted_content = {}
    
    if content_type == "table":
        # For tables, extract the table data, markdown, and new html representation
        if "table_data" in metadata:
            formatted_content["table_data"] = metadata["table_data"]
        if "markdown_table" in metadata:
            formatted_content["markdown_table"] = metadata["markdown_table"]
        # NEW: inline table HTML string for direct rendering
        if "table_html" in metadata and metadata["table_html"]:
            formatted_content["table_html"] = metadata["table_html"]
        elif "table_data" in metadata and metadata["table_data"]:
            # FALLBACK: Generate table_html on-the-fly for existing chunks that don't have it
            try:
                from document_extractor import format_table_data_as_html
                formatted_content["table_html"] = format_table_data_as_html(metadata["table_data"])
                logger.info("Generated table_html on-the-fly for legacy chunk")
            except Exception as e:
                logger.warning(f"Failed to generate table_html on-the-fly: {e}")
        if "text_table" in metadata:
            formatted_content["text_table"] = metadata["text_table"]
    
    elif content_type == "image":
        # Prefer explicit list of images (array of URLs/base64)
        if "images" in metadata and isinstance(metadata["images"], list):
            formatted_content["images"] = metadata["images"]
            # Convenience: set first image as base64_data or url for quick access
            if len(metadata["images"]) > 0:
                first_img = metadata["images"][0]
                if isinstance(first_img, str):
                    # Assume string URL
                    formatted_content["image_url"] = first_img
                elif isinstance(first_img, dict):
                    if "storage_url" in first_img:
                        formatted_content["image_url"] = first_img["storage_url"]
                    if "base64_data" in first_img:
                        formatted_content["base64_data"] = first_img["base64_data"]
        # Backwards compatibility paths
        elif "visual_content" in metadata and "images" in metadata["visual_content"]:
            formatted_content["images"] = metadata["visual_content"]["images"]
        # Direct base64 fallback
        if "base64_data" in metadata and "base64_data" not in formatted_content:
            formatted_content["base64_data"] = metadata["base64_data"]
        if "image_url" in metadata and "image_url" not in formatted_content:
            formatted_content["image_url"] = metadata["image_url"]
    
    # Add common fields
    if "storage_url" in metadata:
        formatted_content["storage_url"] = metadata["storage_url"]
    if "visual_content_type" in metadata:
        formatted_content["visual_content_type"] = metadata["visual_content_type"]
    if "display_type" in metadata:
        formatted_content["display_type"] = metadata["display_type"]
    # Pass through raw metadata if needed for debugging
    formatted_content["_raw"] = metadata
    
    return formatted_content

def generate_clean_answer_with_sources(query: str, chunks: List[Dict[str, Any]], source_type: str, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a clean answer with properly formatted sources for a specific source type.
    ONLY tracks chunks that are actually used in the LLM prompt as sources.

    Args:
        query: The user's question
        chunks: List of chunks from a single source type (document OR website)
        source_type: Either "document" or "website"
        model_id: LLM model to use
        extract_format: Preferred format for the extraction

    Returns:
        Tuple of (answer_text, clean_sources_list, visual_content_found, visual_content_types)
    """
    try:
        logger.info(f"🔍 Generating clean {source_type} answer from {len(chunks)} total chunks")

        # STEP 1: Filter and rank chunks by relevance - Use proper threshold
        min_threshold = 0.4  # Use 0.4 threshold for meaningful relevance as per requirements

        # Filter chunks by relevance first
        relevant_chunks = []
        for chunk in chunks:
            similarity = chunk.get('similarity', 0.0)
            chunk_text = chunk.get('text', '').strip()

            # FIXED: Skip chunks with very low similarity, empty text, or insufficient content
            if similarity < min_threshold or not chunk_text or len(chunk_text) < 20:
                logger.info(f"⏭️  [FIXED] Skipping {source_type} chunk with similarity {similarity:.3f} (below threshold {min_threshold}) or insufficient text")
                continue

            relevant_chunks.append(chunk)

        # If no relevant chunks found, return empty result
        if not relevant_chunks:
            logger.info(f"❌ No relevant {source_type} chunks found above threshold {min_threshold}")
            return f"I couldn't find relevant information in the {source_type} content to answer your question.", [], False, []

        # STEP 2: Select TOP chunks to actually use in LLM prompt (CRITICAL CHANGE)
        # Only use the most relevant chunks, not all relevant chunks
        max_chunks_to_use = 4 if source_type == "document" else 3  # FIXED: Reduced chunks used
        
        # Sort by similarity and take top chunks
        relevant_chunks_sorted = sorted(relevant_chunks, key=lambda x: x.get('similarity', 0), reverse=True)
        chunks_to_use = relevant_chunks_sorted[:max_chunks_to_use]
        
        logger.info(f"📊 Using TOP {len(chunks_to_use)} chunks out of {len(relevant_chunks)} relevant chunks (out of {len(chunks)} total)")
        
        # Log which chunks we're actually using
        for i, chunk in enumerate(chunks_to_use, 1):
            similarity = chunk.get('similarity', 0)
            if source_type == "document":
                filename = chunk.get('filename', 'Unknown')
                page = chunk.get('page', 'Unknown')
                logger.info(f"   ✅ {i}. Using: {filename} (Page {page}) - Similarity: {similarity:.3f}")
            else:
                url = chunk.get('url', 'Unknown URL')
                logger.info(f"   ✅ {i}. Using: {url} - Similarity: {similarity:.3f}")

        # STEP 3: Build context ONLY from chunks we're actually using
        context_texts = []
        used_sources_tracker = {}  # FIXED: Track ONLY chunks actually used in LLM prompt
        visual_content_found = False
        visual_content_types = []

        for chunk in chunks_to_use:  # ONLY iterate over chunks we're using
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            if source_type == "document":
                # Document source processing
                document_id = chunk.get("document_id")
                # First try to get filename from document_id
                filename = None
                if document_id:
                    try:
                        from supabase_client import supabase
                        doc_query = f"SELECT COALESCE(display_name, file_name, name) as filename FROM documents WHERE id = '{document_id}'"
                        doc_result = supabase.execute_query(doc_query)
                        if doc_result and len(doc_result) > 0:
                            filename = doc_result[0]['filename']
                    except Exception as e:
                        logger.error(f"Error retrieving document name: {str(e)}")
                
                # Fall back to chunk metadata if needed
                if not filename:
                    filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                
                page = chunk.get("page") or chunk.get("page_number") or 1

                # Check for visual content
                metadata = chunk.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                visual_content_type = metadata.get("visual_content_type")
                storage_url = metadata.get("storage_url")
                display_type = metadata.get("display_type", "text")

                # Track visual content
                if content_type != "text":
                    visual_content_found = True
                    if content_type not in visual_content_types:
                        visual_content_types.append(content_type)

                # Enhanced context for visual content
                if content_type in ["table", "image", "chart_diagram"]:
                    context_texts.append(f"From '{filename}' (page {page}, {content_type}, relevance: {similarity:.2f}):\n{chunk_text}\n")
                else:
                    context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    source_key = f"{filename}"
                    
                logger.info(f"📝 [FIXED] Chunk {i+1}: Using '{filename}' page {page} (similarity: {similarity:.3f})")
                    
                if source_key not in used_sources_tracker:
                    used_sources_tracker[source_key] = {
                        "source_type": "document",
                        "filename": filename,
                        "name": os.path.basename(filename),
                        "pages": [],
                        "content_type": content_type,
                        "visual_content_type": visual_content_type,
                        "storage_url": storage_url,
                        "display_type": display_type,
                        "visual_content": format_visual_content_for_frontend(metadata, content_type) if content_type != "text" else None
                    }

                # Add page if not already present
                if page not in used_sources_tracker[source_key]["pages"]:
                    used_sources_tracker[source_key]["pages"].append(page)

            elif source_type == "website":
                # Website source processing - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                # Add to context
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication
                source_key = url
                if source_key not in used_sources_tracker:
                    used_sources_tracker[source_key] = {
                        "source_type": "website",
                        "url": url,
                        "title": chunk.get("title", ""),
                        "pages": []  # Added pages field for consistency
                    }

        # STEP 4: Generate answer using ONLY the selected chunks
        if not context_texts:
            logger.info(f"❌ No valid context generated from {source_type} chunks")
            return f"I couldn't extract meaningful information from the {source_type} content to answer your question.", [], visual_content_found, visual_content_types

        # Combine context
        combined_context = "\n".join(context_texts)
        
        logger.info(f"🤖 Generating {source_type} answer with context from {len(chunks_to_use)} chunks")
        logger.info(f"📊 Context length: {len(combined_context)} characters")

        # Generate the answer using only selected chunks.
        # IMPORTANT FIX: Provide a strict system prompt so Gemini/LLM
        # does NOT return the generic fallback message when context exists.

        strict_system_prompt = f"""You are RailGPT, an AI assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. Use ONLY the information in the provided context to answer the question.
2. Assume the context is relevant even if it looks limited. Attempt to answer based on it.
3. If the context lacks some details, begin with: \"Based on the available document information:\" and give the best possible answer.
4. Do NOT output the sentence \"I couldn't find any valid information to answer your question.\" unless the context is completely empty.
5. Do NOT rely on general knowledge. Stick strictly to the context.
6. Format the response clearly using paragraphs and bullet points where helpful.
"""

        answer_result = generate_llm_answer(
            query=query,
            similar_chunks=chunks_to_use,  # Pass only chunks actually being used
            system_prompt=strict_system_prompt,
            model_id=model_id,
            extract_format=extract_format
        )

        # CRITICAL FIX: Handle both string and tuple returns from generate_llm_answer
        if isinstance(answer_result, tuple):
            # If generate_llm_answer returns a tuple, extract just the answer text
            answer = answer_result[0] if len(answer_result) > 0 and answer_result[0] else ""
        else:
            # If it returns a string, use it directly
            answer = str(answer_result) if answer_result else ""

        if not answer or answer.strip() == "":
            logger.warning(f"⚠️  Empty answer generated for {source_type} query")
            return f"I couldn't generate a meaningful answer from the {source_type} content.", [], visual_content_found, visual_content_types

        # STEP 5: Build clean sources list from ONLY the chunks we used
        clean_sources = []
        
        for source_data in used_sources_tracker.values():
            if source_type == "document":
                # Calculate relevance score for this document source
                # Find the highest similarity chunk for this document among USED chunks
                max_similarity = 0
                for chunk in chunks_to_use:  # Only check chunks we actually used
                    if chunk.get("filename") == source_data["filename"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                logger.info(f"📄 Source: '{source_data['name']}' - Pages used: {pages}")
                
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"

                # Enhanced source with visual content information
                clean_source = {
                    "source_type": "document",
                    "filename": source_data["filename"],
                    "name": source_data["name"],
                    "page": pages[0],  # First page for link generation
                    "pages": pages,    # All pages used
                    "link": f"/viewer?file={source_data['filename']}&page={pages[0]}",
                    "display_text": f"{source_data['name']} – {page_ref}",
                    "relevance_score": max_similarity,
                    "content_type": source_data.get("content_type", "text"),
                    "visual_content": source_data.get("visual_content"),
                    "storage_url": source_data.get("storage_url"),
                    "display_type": source_data.get("display_type", "text")
                }
                clean_sources.append(clean_source)

            elif source_type == "website":
                # Calculate relevance score for this website source among USED chunks
                max_similarity = 0
                for chunk in chunks_to_use:  # Only check chunks we actually used
                    if chunk.get("url") == source_data["url"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                clean_source = {
                    "source_type": "website",
                    "url": source_data["url"],
                    "title": source_data.get("title", ""),
                    "name": source_data.get("title", source_data["url"]),
                    "relevance_score": max_similarity,
                    "display_text": source_data.get("title", source_data["url"])
                }
                clean_sources.append(clean_source)

        # Sort sources by relevance
        clean_sources.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

        logger.info(f"✅ Generated {source_type} answer with {len(clean_sources)} TRUE sources (from chunks actually used)")
        
        # Log final sources for verification
        for i, source in enumerate(clean_sources, 1):
            if source_type == "document":
                logger.info(f"   📄 {i}. {source['display_text']} (Relevance: {source['relevance_score']:.3f})")
            else:
                logger.info(f"   🌐 {i}. {source['display_text']} (Relevance: {source['relevance_score']:.3f})")

        return answer, clean_sources, visual_content_found, visual_content_types

    except Exception as e:
        logger.error(f"❌ Error in generate_clean_answer_with_sources: {str(e)}")
        return f"I encountered an error while processing the {source_type} content.", [], False, []

# Database optimization endpoints
@app.post("/api/database/maintenance")
async def run_database_maintenance():
    """Run database maintenance tasks for optimization."""
    try:
        if not db_optimizer:
            raise HTTPException(status_code=503, detail="Database optimizer not available")

        logger.info("Running database maintenance...")
        await db_optimizer.run_maintenance()

        return {"message": "Database maintenance completed successfully"}

    except Exception as e:
        logger.error(f"Error running database maintenance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database maintenance failed: {str(e)}")

@app.get("/api/database/stats")
async def get_database_stats():
    """Get database performance statistics."""
    try:
        if not db_optimizer:
            raise HTTPException(status_code=503, detail="Database optimizer not available")

        stats = await db_optimizer.get_performance_stats()
        return stats

    except Exception as e:
        logger.error(f"Error getting database stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get database stats: {str(e)}")

# Memory management endpoints
@app.get("/api/memory/stats")
async def get_memory_stats():
    """Get comprehensive memory and cache statistics."""
    try:
        if not MEMORY_MANAGEMENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="Memory management not available")

        stats = memory_manager.get_comprehensive_stats()
        return stats

    except Exception as e:
        logger.error(f"Error getting memory stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get memory stats: {str(e)}")

@app.post("/api/memory/cleanup")
async def force_memory_cleanup():
    """Force memory cleanup and garbage collection."""
    try:
        if not MEMORY_MANAGEMENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="Memory management not available")

        memory_manager.memory_monitor.force_cleanup()
        stats = memory_manager.get_comprehensive_stats()

        return {
            "message": "Memory cleanup completed",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Error during memory cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Memory cleanup failed: {str(e)}")

@app.post("/api/memory/optimize")
async def optimize_memory_for_scale():
    """Optimize memory settings for large-scale processing."""
    try:
        if not MEMORY_MANAGEMENT_AVAILABLE:
            raise HTTPException(status_code=503, detail="Memory management not available")

        memory_manager.optimize_for_large_dataset()
        stats = memory_manager.get_comprehensive_stats()

        return {
            "message": "Memory optimization completed for large dataset processing",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Error optimizing memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Memory optimization failed: {str(e)}")

# Performance optimization endpoints
@app.get("/api/performance/stats")
async def get_performance_stats():
    """Get comprehensive performance statistics."""
    try:
        if not performance_manager:
            raise HTTPException(status_code=503, detail="Performance manager not available")

        stats = performance_manager.get_comprehensive_stats()
        return stats

    except Exception as e:
        logger.error(f"Error getting performance stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance stats: {str(e)}")

@app.post("/api/performance/vector-search")
async def optimized_vector_search(
    query_embedding: List[float],
    top_k: int = 30,
    threshold: float = 0.4,
    use_approximate: bool = True
):
    """Perform optimized vector search with performance improvements."""
    try:
        if not performance_manager:
            raise HTTPException(status_code=503, detail="Performance manager not available")

        results = await performance_manager.vector_optimizer.approximate_vector_search(
            query_embedding=query_embedding,
            top_k=top_k,
            threshold=threshold,
            use_approximate=use_approximate
        )

        return {
            "results": results,
            "count": len(results),
            "search_type": "approximate" if use_approximate else "exact"
        }

    except Exception as e:
        logger.error(f"Error in optimized vector search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Vector search failed: {str(e)}")

@app.post("/api/performance/process-documents")
async def async_process_documents(
    documents: List[Dict[str, Any]],
    batch_size: int = 50
):
    """Process documents asynchronously for improved performance."""
    try:
        if not performance_manager:
            raise HTTPException(status_code=503, detail="Performance manager not available")

        # Simple processing function (can be customized)
        def process_doc(doc):
            doc['processed_at'] = datetime.now().isoformat()
            doc['processing_status'] = 'completed'
            return doc

        # Set batch size
        performance_manager.async_processor.batch_size = batch_size

        results = await performance_manager.async_processor.process_documents_async(
            documents=documents,
            processor_func=process_doc
        )

        return {
            "processed_documents": results,
            "total_processed": len(results),
            "batch_size": batch_size
        }

    except Exception as e:
        logger.error(f"Error in async document processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Async processing failed: {str(e)}")

# Health check endpoints
@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with all system components"""
    try:
        if not HEALTH_CHECK_AVAILABLE:
            return {
                "status": "unknown",
                "message": "Health check system not available",
                "timestamp": datetime.now().isoformat()
            }

        system_health = await health_manager.run_all_checks()
        return health_manager.to_dict(system_health)

    except Exception as e:
        logger.error(f"Error running detailed health check: {str(e)}")
        return {
            "status": "unhealthy",
            "message": f"Health check failed: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }

@app.get("/health/live")
async def liveness_probe():
    """Kubernetes liveness probe endpoint"""
    try:
        # Simple check to ensure the application is running
        return {"status": "alive", "timestamp": datetime.now().isoformat()}
    except Exception as e:
        logger.error(f"Liveness probe failed: {str(e)}")
        raise HTTPException(status_code=503, detail="Service not alive")

@app.get("/health/ready")
async def readiness_probe():
    """Kubernetes readiness probe endpoint"""
    try:
        if not HEALTH_CHECK_AVAILABLE:
            raise HTTPException(status_code=503, detail="Health check system not available")

        # Check critical components for readiness
        db_check = DatabaseHealthCheck(supabase)
        db_result = await db_check.check()

        if db_result.status.value in ["healthy", "degraded"]:
            return {
                "status": "ready",
                "database": db_result.status.value,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=503, detail="Service not ready - database unhealthy")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness probe failed: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")

@app.get("/metrics")
async def metrics_endpoint():
    """Prometheus-style metrics endpoint"""
    try:
        metrics = []

        # System metrics
        if MEMORY_MANAGEMENT_AVAILABLE:
            memory_stats = memory_manager.get_comprehensive_stats()
            metrics.extend([
                f"railgpt_memory_usage_percent {memory_stats['memory']['usage_percent']}",
                f"railgpt_memory_available_gb {memory_stats['memory']['available_gb']}",
                f"railgpt_cache_embedding_size {memory_stats['caches']['embedding_cache']['size']}",
                f"railgpt_cache_query_size {memory_stats['caches']['query_cache']['size']}",
                f"railgpt_documents_processed {memory_stats['processing']['documents_processed']}"
            ])

        # Performance metrics
        if performance_manager:
            perf_stats = performance_manager.get_comprehensive_stats()
            metrics.extend([
                f"railgpt_connection_pool_active {perf_stats['connection_pool']['active_connections']}",
                f"railgpt_connection_pool_success_rate {perf_stats['connection_pool']['success_rate']}",
                f"railgpt_async_processor_active_tasks {perf_stats['async_processor']['active_tasks']}",
                f"railgpt_vector_search_cache_size {perf_stats['vector_search']['cache_size']}"
            ])

        # Health check metrics
        if HEALTH_CHECK_AVAILABLE:
            last_health = health_manager.get_last_results()
            if last_health:
                metrics.append(f"railgpt_uptime_seconds {last_health.uptime_seconds}")
                for check in last_health.checks:
                    status_value = 1 if check.status.value == "healthy" else 0
                    metrics.append(f"railgpt_health_check{{check=\"{check.name}\"}} {status_value}")
                    metrics.append(f"railgpt_health_check_response_time_ms{{check=\"{check.name}\"}} {check.response_time_ms}")

        return "\n".join(metrics)

    except Exception as e:
        logger.error(f"Error generating metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Metrics generation failed: {str(e)}")

# Chunks endpoint
@app.get("/api/chunks")
async def get_chunks():
    # Return document chunks without embeddings for response size efficiency
    chunks_without_embeddings = []
    for chunk in DOCUMENT_CHUNKS:
        chunk_copy = dict(chunk)
        if "embedding" in chunk_copy:
            del chunk_copy["embedding"]
        chunks_without_embeddings.append(chunk_copy)

    return chunks_without_embeddings

@app.post("/api/query", response_model=QueryResponse)
async def query(request: QueryRequest):
    """
    RailGPT Strict 3-Tier Priority Query Processor

    Implements STRICT priority logic:
    1. Document chunks (highest priority) - 0.4 threshold
    2. Website chunks (medium priority) - 0.4 threshold
    3. LLM fallback (lowest priority) - only when NO relevant chunks found

    Returns structured response with proper llm_fallback_used flag and source attribution.
    """
    # PERFORMANCE MONITORING: Track query processing time
    start_time = time.time()
    logger.info(f"🚀 [RAILGPT-STRICT] Processing query: '{request.query}' using model: {request.model}")

    # PERFORMANCE OPTIMIZATION: Check cache first
    cache_key = get_cache_key(request.query, request.model)
    if cache_key in QUERY_CACHE:
        cached_result, cache_time = QUERY_CACHE[cache_key]
        if time.time() - cache_time < CACHE_TTL:
            logger.info(f"⚡ [CACHE HIT] Returning cached result for query (saved {time.time() - start_time:.2f}s)")
            return cached_result

    # Initialize variables
    used_fallback_model = False
    original_model = request.model
    fallback_reason = None
    model_id = request.model

    # Core response variables - STRICT PRIORITY SYSTEM
    final_answer = ""
    final_sources = []
    document_sources = []
    website_sources = []
    llm_fallback_used = False
    visual_content_found = False
    visual_content_types = []

    # Tier tracking for strict priority enforcement
    document_answer = None
    website_answer = None

    try:
        # Validate the requested model and API key
        if not llm_router.is_model_available(request.model):
            logger.warning(f"Model {request.model} not available, falling back to default model")
            model_id = llm_router.DEFAULT_MODEL
            used_fallback_model = True
            fallback_reason = f"Model '{request.model}' is not available"
        else:
            # Check if API key is valid for this model
            model_config = llm_router.get_model_config(model_id)
            key_status = llm_router.check_api_key_validity(model_config["provider"])
            if not key_status.get("valid", False):
                logger.warning(f"Invalid API key for {model_id}, falling back to default model")
                model_id = llm_router.DEFAULT_MODEL
                used_fallback_model = True
                fallback_reason = f"API key for {original_model} is invalid: {key_status.get('message', '')}"

        logger.info("=== 🎯 RAILGPT STRICT 3-TIER PRIORITY SYSTEM STARTING ===")

        # STEP 1: Generate query embedding for vector search (CRITICAL - ALWAYS DO THIS FIRST)
        logger.info("🔍 [STEP 1] Generating query embedding for vector search...")
        query_embedding = None
        embedding_start_time = time.time()
        try:
            # PERFORMANCE OPTIMIZATION: Use cached embedding generation
            query_embedding = cached_embedding_generation(request.query, model_id)
            embedding_time = time.time() - embedding_start_time
            logger.info(f"✅ [STEP 1] Query embedding generated successfully in {embedding_time:.2f}s (size: {len(query_embedding) if query_embedding else 0})")
        except Exception as e:
            logger.error(f"❌ [STEP 1] Failed to generate query embedding: {str(e)}")
            # Don't stop here - we can still do text-based search

        # ✅ 1️⃣ PRIORITY 1: DOCUMENT CHUNKS (HIGHEST PRIORITY - PROPER THRESHOLD)
        logger.info("📄 [PRIORITY 1] SEARCHING DOCUMENT CHUNKS (HIGHEST PRIORITY)")
        document_chunks = []

        # PRIORITY FIX: Try text search first for FSDS queries
        if "fsds" in request.query.lower():
            try:
                logger.info("🎯 PRIORITY: Detected FSDS query, using direct text search")
                fsds_result = supabase.table('document_chunks').select('*').ilike('text', '%fsds%').execute()

                if fsds_result.data:
                    logger.info(f"🎯 PRIORITY: Found {len(fsds_result.data)} FSDS chunks via text search")
                    # Add high similarity scores for exact content matches
                    for chunk in fsds_result.data:
                        chunk['similarity'] = 0.95  # Very high similarity for exact content matches
                        chunk['source_type'] = 'document'
                        # Get filename from document lookup
                        if chunk.get('document_id'):
                            try:
                                doc_result = supabase.table('documents').select('filename, display_name').eq('id', chunk['document_id']).execute()
                                if doc_result.data:
                                    chunk['filename'] = doc_result.data[0].get('display_name') or doc_result.data[0].get('filename', 'FSDS Document')
                                else:
                                    chunk['filename'] = 'FSDS Document'
                            except:
                                chunk['filename'] = 'FSDS Document'
                        else:
                            chunk['filename'] = 'FSDS Document'
                        chunk['page'] = chunk.get('page_number', 1)
                    document_chunks = fsds_result.data
                    logger.info(f"🎯 PRIORITY: Using {len(document_chunks)} FSDS chunks with high similarity scores")
                else:
                    logger.info("🎯 PRIORITY: No FSDS chunks found via text search")
                    document_chunks = []
            except Exception as e:
                logger.error(f"🎯 PRIORITY: Text search failed: {str(e)}")
                document_chunks = []

        # Always try vector search first with proper 0.4 threshold for meaningful relevance
        if not document_chunks and query_embedding:
            try:
                document_chunks = search_supabase_document_chunks_enhanced(
                    query_embedding=query_embedding,
                    query_text=request.query,
                    use_hybrid_search=request.use_hybrid_search,
                    visual_query_info=detect_visual_query(request.query),
                    top_k=30,  # Get more chunks for better coverage
                    min_threshold=0.4  # ✅ Proper threshold for meaningful document relevance
                )

                # ✅ 2️⃣ PROPER THRESHOLD: Keep chunks >= 0.4 for meaningful relevance
                if document_chunks:
                    # Filter with proper 0.4 threshold - keep only meaningful relevance
                    valid_chunks = [chunk for chunk in document_chunks if chunk.get('similarity', 0) >= 0.4]
                    
                    logger.info(f"🔍 [PRIORITY 1] Vector search found {len(document_chunks)} total chunks:")
                    logger.info(f"   ✅ Valid chunks (≥0.4): {len(valid_chunks)} chunks")
                    
                    if valid_chunks:
                        # Sort by similarity and keep all valid chunks
                        valid_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
                        document_chunks = valid_chunks
                        logger.info(f"✅ [PRIORITY 1] Using {len(document_chunks)} meaningful document chunks")
                        
                        # Log top similarities for debugging
                        top_sims = [f"{c.get('similarity', 0):.3f}" for c in document_chunks[:5]]
                        logger.info(f"   📊 Top similarities: {top_sims}")
                    else:
                        document_chunks = []
                        logger.info(f"❌ [PRIORITY 1] No document chunks meet minimum 0.4 threshold")
                else:
                    logger.info(f"🔍 [PRIORITY 1] Vector search found 0 document chunks")

            except Exception as e:
                logger.error(f"❌ [PRIORITY 1] Vector search failed: {str(e)}")
                document_chunks = []

        # ✅ 3️⃣ TRUE SOURCE TRACKING: Process document chunks if any found
        if document_chunks:
            logger.info(f"✅ [PRIORITY 1] FOUND {len(document_chunks)} relevant document chunks (≥0.01 similarity)")

            # ✅ 7️⃣ PERFORMANCE: Log chunk details for debugging
            for i, chunk in enumerate(document_chunks[:5], 1):
                similarity = chunk.get('similarity', 0)
                filename = chunk.get('filename', 'Unknown')
                page = chunk.get('page', chunk.get('page_number', 'Unknown'))
                chunk_id = chunk.get('id', 'Unknown')
                logger.info(f"   📄 {i}. ID: {chunk_id} | File: {filename} | Page: {page} | Similarity: {similarity:.3f}")

            # Generate document answer with ONLY contributing sources
            try:
                document_answer, document_sources, doc_visual_found, doc_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=document_chunks,
                    source_type="document",
                    model_id=model_id,
                    extract_format=request.extract_format
                )

                if document_answer and document_sources:
                    logger.info(f"✅ [PRIORITY 1] Document answer generated with {len(document_sources)} TRUE sources")
                    visual_content_found = visual_content_found or doc_visual_found
                    visual_content_types.extend(doc_visual_types or [])

                    # ✅ 1️⃣ PRIORITY: Document answer found - set flags
                    final_answer = document_answer
                    final_sources = document_sources
                    llm_fallback_used = False  # ✅ 4️⃣ LLM Fallback Flag
                    logger.info("🔵 [PRIORITY 1] DOCUMENT ANSWER FOUND - SOURCE: DOCUMENTS")
                else:
                    logger.info(f"⚠️ [PRIORITY 1] Document answer generation returned no valid contributing sources")
                    document_answer = None
                    document_sources = []

            except Exception as e:
                logger.error(f"❌ [PRIORITY 1] Error generating document answer: {str(e)}")
                document_answer = None
                document_sources = []
        else:
            logger.info("❌ [PRIORITY 1] No document chunks meet 0.4 similarity threshold")

        # ✅ 1️⃣ PRIORITY 2: WEBSITE CHUNKS (MEDIUM PRIORITY - ONLY IF NO DOCUMENTS)
        if not final_answer:  # Only search websites if no document answer was found
            logger.info("🌐 [PRIORITY 2] SEARCHING WEBSITE CHUNKS (MEDIUM PRIORITY)")
            website_chunks = []

            # Always try vector search first with proper 0.4 threshold for meaningful relevance
            if query_embedding:
                try:
                    website_chunks = search_supabase_website_chunks(
                        query_embedding=query_embedding,
                        query_text=request.query,
                        use_hybrid_search=request.use_hybrid_search,
                        top_k=25,  # Get more website chunks
                        min_threshold=0.4  # ✅ Proper threshold for meaningful website relevance
                    )

                    # ✅ 2️⃣ PROPER THRESHOLD: Keep website chunks >= 0.4 for meaningful relevance
                    if website_chunks:
                        # Filter with proper 0.4 threshold - keep only meaningful relevance
                        valid_chunks = [chunk for chunk in website_chunks if chunk.get('similarity', 0) >= 0.4]
                        
                        logger.info(f"🔍 [PRIORITY 2] Vector search found {len(website_chunks)} total chunks:")
                        logger.info(f"   ✅ Valid chunks (≥0.01): {len(valid_chunks)} chunks")
                        
                        if valid_chunks:
                            # Sort by similarity and keep all valid chunks
                            valid_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
                            website_chunks = valid_chunks
                            logger.info(f"✅ [PRIORITY 2] Using {len(website_chunks)} meaningful website chunks")
                            
                            # Log top similarities for debugging  
                            top_sims = [f"{c.get('similarity', 0):.3f}" for c in website_chunks[:5]]
                            logger.info(f"   📊 Top similarities: {top_sims}")
                        else:
                            website_chunks = []
                            logger.info(f"❌ [PRIORITY 2] No website chunks meet minimum 0.4 threshold")
                    else:
                        logger.info(f"🔍 [PRIORITY 2] Vector search found 0 website chunks")

                except Exception as e:
                    logger.error(f"❌ [TIER 2] Website vector search failed: {str(e)}")
                    website_chunks = []

            # PRIORITY PROCESSING: Process website chunks if any found
            if website_chunks:
                logger.info(f"✅ [TIER 2] FOUND {len(website_chunks)} relevant website chunks (≥0.03 similarity)")

                # Log chunk details for debugging
                for i, chunk in enumerate(website_chunks[:5], 1):
                    similarity = chunk.get('similarity', 0)
                    url = chunk.get('url', 'Unknown URL')
                    chunk_id = chunk.get('id', 'Unknown')
                    logger.info(f"   🌐 {i}. ID: {chunk_id} | URL: {url} | Similarity: {similarity:.3f}")

                # Generate website answer with ONLY contributing sources
                try:
                    website_answer, website_sources, web_visual_found, web_visual_types = generate_clean_answer_with_sources(
                        query=request.query,
                        chunks=website_chunks,
                        source_type="website",
                        model_id=model_id,
                        extract_format=request.extract_format
                    )

                    if website_answer and website_sources:
                        logger.info(f"✅ [TIER 2] Website answer generated successfully with {len(website_sources)} contributing sources")
                        visual_content_found = visual_content_found or web_visual_found
                        visual_content_types.extend(web_visual_types or [])

                        # PRIORITY: Website answer found - set flags
                        final_answer = website_answer
                        final_sources = website_sources
                        llm_fallback_used = False
                        logger.info("🟡 [TIER 2] WEBSITE ANSWER FOUND")
                    else:
                        logger.info(f"⚠️ [TIER 2] Website answer generation returned no valid contributing sources")
                        website_answer = None
                        website_sources = []

                except Exception as e:
                    logger.error(f"❌ [TIER 2] Error generating website answer: {str(e)}")
                    website_answer = None
                    website_sources = []
            else:
                logger.info("❌ [TIER 2] No website chunks meet 0.03 similarity threshold")
        else:
            logger.info("⏭️ [TIER 2] SKIPPING website search - Document answer already found")

        # TIER 3: LLM Fallback (LOWEST PRIORITY - ONLY IF NO RELEVANT CHUNKS)
        if not final_answer:  # STRICT PRIORITY: Only use LLM if no document or website answer
            if request.fallback_enabled:
                logger.info("🔴 [TIER 3] USING LLM FALLBACK (no relevant chunks found in any source)")
                llm_fallback_used = True

                # Generate LLM-only answer with clean prompt (NO FAKE SOURCES)
                fallback_prompt = f"""You are RailGPT, an AI assistant specializing in Indian Railways and technical documentation.

The user's question could not be answered from the uploaded documents or extracted websites because no relevant content was found that meets the similarity threshold.

Please provide a helpful answer using your general knowledge about railways, engineering, or the topic asked.

If this is about Indian Railways, provide detailed technical information.
Format your answer clearly with paragraphs and bullet points where appropriate.

USER QUESTION: {request.query}

Provide a comprehensive answer based on your knowledge."""

                try:
                    final_answer = llm_router.generate_answer(
                        query=request.query,
                        context="",  # No context for pure LLM fallback
                        system_prompt=fallback_prompt,
                        model_id=model_id
                    )
                    logger.info("✅ [TIER 3] LLM fallback answer generated successfully")
                except Exception as e:
                    logger.error(f"❌ [TIER 3] LLM fallback failed: {str(e)}")
                    final_answer = "I apologize, but I couldn't find relevant information in the uploaded documents or extracted websites, and the fallback system is currently unavailable."

                # STRICT: Clear all sources for LLM fallback (NO fake chunk sources)
                final_sources = []
                document_sources = []
                website_sources = []
                document_answer = None
                website_answer = None

            else:
                # No relevant content found and fallback disabled
                logger.info("❌ [NO MATCH] No content found and fallback disabled")
                final_answer = "No relevant information found in uploaded documents or extracted websites. Please try enabling the LLM fallback option or upload more relevant documents."
                final_sources = []
                document_sources = []
                website_sources = []
                document_answer = None
                website_answer = None
                llm_fallback_used = False
        else:
            logger.info(f"✅ [PRIORITY COMPLETE] Using {'document' if document_answer else 'website'} answer - LLM fallback not needed")

        # Add model fallback information if needed
        if used_fallback_model and fallback_reason:
            model_info = f"Note: Using {model_id} instead of {original_model}. {fallback_reason}."
            if final_answer:
                final_answer = f"{model_info}\n\n{final_answer}"

        # FINAL: Logging and response preparation
        logger.info("📋 [FINAL] STRICT 3-TIER PRIORITY RESULTS SUMMARY")
        logger.info(f"   📄 Document chunks processed: {len(document_chunks) if 'document_chunks' in locals() else 0}")
        logger.info(f"   🌐 Website chunks processed: {len(website_chunks) if 'website_chunks' in locals() else 0}")
        logger.info(f"   📄 Document sources in response: {len(document_sources)}")
        logger.info(f"   🌐 Website sources in response: {len(website_sources)}")
        logger.info(f"   📊 Total sources in response: {len(final_sources)}")
        logger.info(f"   🤖 LLM fallback used: {llm_fallback_used}")
        logger.info(f"   🎨 Visual content found: {visual_content_found}")
        logger.info(f"   🏷️ Visual content types: {visual_content_types}")
        logger.info(f"   🎯 Answer length: {len(final_answer)} characters")
        logger.info(f"   ✅ Priority tier used: {'Document (Tier 1)' if document_answer else 'Website (Tier 2)' if website_answer else 'LLM Fallback (Tier 3)'}")

        # Remove duplicate visual content types
        visual_content_types = list(set(visual_content_types)) if visual_content_types else []

        # Create final response with CORRECT flag name
        response = QueryResponse(
            answer=final_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=final_sources,
            document_sources=document_sources,
            website_sources=website_sources,
            llm_model=model_id,
            llm_fallback=llm_fallback_used,  # Legacy field for backward compatibility
            llm_fallback_used=llm_fallback_used,
            visual_content_found=visual_content_found,
            visual_content_types=visual_content_types if visual_content_types else None
        )

        # PERFORMANCE OPTIMIZATION: Cache successful responses
        total_time = time.time() - start_time
        if total_time < 10:  # Only cache fast responses
            QUERY_CACHE[cache_key] = (response, time.time())

        logger.info(f"⏱️ [PERFORMANCE] Total query processing time: {total_time:.2f} seconds")
        if total_time > 5:
            logger.warning(f"⚠️ [PERFORMANCE] Query took longer than 5 seconds: {total_time:.2f}s")

        return response

    except Exception as e:
        logger.error(f"💥 [ERROR] Critical error processing query: {str(e)}")
        import traceback
        logger.error(f"💥 [ERROR] Traceback: {traceback.format_exc()}")
        
        return QueryResponse(
            answer=f"Sorry, an error occurred while processing your query: {str(e)}",
            sources=[],
            document_sources=[],
            website_sources=[],
            llm_model=llm_router.DEFAULT_MODEL,
            llm_fallback=True,
            llm_fallback_used=True,
            visual_content_found=False,
            visual_content_types=None
        )

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None),
    extract_tables: Optional[bool] = Form(True),
    extract_images: Optional[bool] = Form(True),
    extract_charts: Optional[bool] = Form(True),
    # 4-level category hierarchy
    main_category_id: Optional[str] = Form(None),
    category_id: Optional[str] = Form(None),
    sub_category_id: Optional[str] = Form(None),
    minor_category_id: Optional[str] = Form(None)
):
    # Enhanced logging for troubleshooting upload issues
    logger.info(f"===== DOCUMENT UPLOAD STARTED =====")
    logger.info(f"Received document: {file.filename}, size: {file.size if hasattr(file, 'size') else 'unknown'} bytes")
    logger.info(f"Uploaded by: {uploaded_by}, role: {role}")
    logger.info(f"Supabase file path: {supabase_file_path}")
    logger.info(f"Received document upload request: {file.filename}")

    # Check if the file has content
    if not file.filename:
        raise HTTPException(status_code=400, detail="File has no filename")

    try:
        # Create uploads directory if it doesn't exist
        uploads_dir = os.path.join("data", "uploads")
        os.makedirs(uploads_dir, exist_ok=True)
        logger.info(f"Using uploads directory: {os.path.abspath(uploads_dir)}")
        
        # Check if a document with the same name already exists and delete its chunks
        try:
            # Use proper SQL escaping for filename
            escaped_filename = file.filename.replace("'", "''")
            logger.info(f"Checking if document '{escaped_filename}' already exists")
            
            # Query to find documents with matching filename or display_name
            existing_doc_query = f"""
            SELECT id, display_name 
            FROM documents 
            WHERE display_name = '{escaped_filename}' 
               OR file_path LIKE '%{escaped_filename}%'
            """
            
            existing_docs = supabase.execute_query(existing_doc_query)
            
            if existing_docs and len(existing_docs) > 0:
                for doc in existing_docs:
                    doc_id = doc.get('id')
                    doc_name = doc.get('display_name')
                    logger.info(f"Found existing document: {doc_name} (id: {doc_id}). Deleting old chunks...")
                    
                    # Delete existing chunks for this document
                    delete_chunks_query = f"DELETE FROM document_chunks WHERE document_id = '{doc_id}'"
                    supabase.execute_query(delete_chunks_query)
                    logger.info(f"Deleted old chunks for document {doc_id}")
        except Exception as cleanup_error:
            logger.warning(f"Error while cleaning up existing document chunks: {str(cleanup_error)}")
            # Continue with upload even if cleanup fails
        
        # Save the file to the uploads directory
        file_path = os.path.join(uploads_dir, file.filename)
        logger.info(f"Saving file to: {os.path.abspath(file_path)}")

        # Read the content before saving to get the actual file size
        content = await file.read()
        file_size = len(content)
        logger.info(f"Received file content size: {file_size} bytes")

        # Reset file position and save
        await file.seek(0)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"File saved to {file_path}")

        # Process the document to extract content including visual elements
        # Pass Supabase file path if available
        try:
            logger.info(f"Starting document extraction from {file_path}")
            logger.info(f"Visual extraction options - Tables: {extract_tables}, Images: {extract_images}, Charts: {extract_charts}")

            # Use visual extraction for PDFs, regular extraction for other files
            _, ext = os.path.splitext(file_path)
            if ext.lower() == '.pdf' and (extract_tables or extract_images or extract_charts):
                document_chunks = extract_document_with_visual_content(
                    file_path=file_path,
                    supabase_file_path=supabase_file_path,
                    uploaded_by=uploaded_by,
                    extract_tables=extract_tables,
                    extract_images=extract_images,
                    extract_charts=extract_charts,
                    main_category_id=main_category_id,
                    category_id=category_id,
                    sub_category_id=sub_category_id,
                    minor_category_id=minor_category_id
                                )
            else:
                document_chunks = extract_document(
                    file_path=file_path,
                    supabase_file_path=supabase_file_path,
                    uploaded_by=uploaded_by,
                    main_category_id=main_category_id,
                    category_id=category_id,
                    sub_category_id=sub_category_id,
                    minor_category_id=minor_category_id
                )
            logger.info(f"Document extraction completed with {len(document_chunks)} chunks")
        except Exception as extract_error:
            logger.error(f"Error during document extraction: {str(extract_error)}")
            import traceback
            logger.error(f"Extraction error traceback: {traceback.format_exc()}")

            # Simple fallback extraction
            logger.info("Attempting fallback extraction...")
            try:
                # Simple text extraction fallback
                with open(file_path, 'rb') as f:
                    # Try to read the file as text
                    sample_content = f.read(4096)  # Read first 4KB to detect text

                is_binary = False
                try:
                    sample_content.decode('utf-8')
                except UnicodeDecodeError:
                    is_binary = True

                if not is_binary:
                    # Simple text extraction for text files
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()

                    # Create a minimal chunk
                    document_chunks = [{
                        "filename": os.path.basename(file_path),
                        "text": text,
                        "source_type": "document",
                        "page": 1,
                        "chunk_id": f"{os.path.splitext(os.path.basename(file_path))[0]}_1_0"
                    }]
                    logger.info(f"Fallback extraction created {len(document_chunks)} simple chunks")
                else:
                    # Binary file we can't parse
                    logger.error("Cannot extract text from binary file using fallback method")
                    document_chunks = []
            except Exception as fallback_error:
                logger.error(f"Fallback extraction also failed: {str(fallback_error)}")
                document_chunks = []

        # Handle text files that failed extraction
        if not document_chunks:
            logger.warning(f"No chunks extracted from document: {file.filename}")

            # For text files and other simple formats, create a basic chunk from file content
            if file.filename.lower().endswith(('.txt', '.md', '.csv', '.log', '.py', '.js', '.html', '.xml', '.json')):
                logger.info(f"Creating basic text chunk for {file.filename}")
                try:
                    # Read the file content as text - use the correct file_path variable
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text_content = f.read()
                    
                    if text_content.strip():
                        # Create a basic document chunk
                        basic_chunk = {
                            'filename': file.filename,
                            'page': 1,
                            'chunk_index': 0,
                            'text': text_content,
                            'page_number': 1,
                            'source_type': 'document',
                            'chunk_type': 'text',
                            'content_type': 'text',
                            'metadata': {
                                'file_type': 'text',
                                'extraction_method': 'direct_text_read',
                                'original_filename': file.filename
                            }
                        }
                        document_chunks = [basic_chunk]
                        logger.info(f"✅ Successfully created basic text chunk with {len(text_content)} characters")
                    else:
                        logger.error(f"File {file.filename} appears to be empty")
                        raise HTTPException(
                            status_code=400,
                            detail=f"The uploaded file '{file.filename}' appears to be empty or contains no readable text."
                        )
                except Exception as text_error:
                    logger.error(f"Error reading text file {file.filename}: {str(text_error)}")
                    raise HTTPException(
                        status_code=400,
                        detail=f"Could not read the text file '{file.filename}'. Error: {str(text_error)}"
                    )
            else:
                logger.error(f"No chunks extracted from document: {file.filename}")
                raise HTTPException(
                    status_code=422,
                    detail=f"Failed to extract content from {file.filename}. Supported text formats: .txt, .md, .csv, .log, .py, .js, .html, .xml, .json. For other formats, ensure the file is not corrupted."
                )

        # Get document_id from the first chunk if available (set by extract_document when using Supabase)
        document_id = None
        if document_chunks and "document_id" in document_chunks[0]:
            document_id = document_chunks[0]["document_id"]

        # Generate embeddings for each chunk (optimized with async processing if available)
        logger.info(f"Generating embeddings for {len(document_chunks)} chunks...")

        if performance_manager and len(document_chunks) > 10:
            # Use async processing for large documents
            logger.info("Using async processing for embedding generation")

            def generate_chunk_embedding(chunk):
                chunk_text = chunk.get("text", "")
                if chunk_text.strip():
                    try:
                        embedding = generate_embedding(chunk_text)
                        chunk["embedding"] = embedding
                        return chunk
                    except Exception as e:
                        logger.error(f"Failed to generate embedding: {e}")
                        import numpy as np
                        np.random.seed(42)
                        chunk["embedding"] = list(np.random.rand(768))
                        return chunk
                else:
                    logger.warning(f"Empty text in chunk, using fallback embedding")
                    import numpy as np
                    np.random.seed(42)
                    chunk["embedding"] = list(np.random.rand(768))
                    return chunk

            # Process chunks asynchronously
            try:
                processed_chunks = await performance_manager.async_processor.process_documents_async(
                    documents=document_chunks,
                    processor_func=generate_chunk_embedding
                )
                document_chunks = processed_chunks
                logger.info(f"Async embedding generation completed for {len(document_chunks)} chunks")
            except Exception as async_error:
                logger.warning(f"Async processing failed, falling back to sequential: {async_error}")
                # Fall back to sequential processing
                for i, chunk in enumerate(document_chunks):
                    chunk_text = chunk.get("text", "")
                    if chunk_text.strip():
                        try:
                            embedding = generate_embedding(chunk_text)
                            chunk["embedding"] = embedding
                            logger.info(f"Generated embedding for chunk {i+1}/{len(document_chunks)}")
                        except Exception as e:
                            logger.error(f"Failed to generate embedding for chunk {i+1}: {e}")
                            import numpy as np
                            np.random.seed(42)
                            chunk["embedding"] = list(np.random.rand(768))
                    else:
                        logger.warning(f"Empty text in chunk {i+1}, skipping embedding generation")
                        import numpy as np
                        np.random.seed(42)
                        chunk["embedding"] = list(np.random.rand(768))
        else:
            # Sequential processing for smaller documents
            for i, chunk in enumerate(document_chunks):
                # Add additional metadata
                if uploaded_by:
                    chunk["uploaded_by"] = uploaded_by
                if role:
                    chunk["role"] = role
                if supabase_file_path:
                    chunk["supabase_file_path"] = supabase_file_path
                if supabase_file_url:
                    chunk["supabase_file_url"] = supabase_file_url

                # Add embedding with error handling
                chunk_text = chunk.get("text", "")
                if chunk_text.strip():
                    try:
                        embedding = generate_embedding(chunk_text)
                        chunk["embedding"] = embedding
                        logger.info(f"Generated embedding for chunk {i+1}/{len(document_chunks)}")
                    except Exception as e:
                        logger.error(f"Failed to generate embedding for chunk {i+1}: {e}")
                        # Use a fallback embedding
                        import numpy as np
                        np.random.seed(42)
                        chunk["embedding"] = list(np.random.rand(768))
                else:
                    logger.warning(f"Empty text in chunk {i+1}, skipping embedding generation")
                    import numpy as np
                    np.random.seed(42)
                    chunk["embedding"] = list(np.random.rand(768))

        # Add metadata to all chunks after processing
        for chunk in document_chunks:
            if uploaded_by:
                chunk["uploaded_by"] = uploaded_by
            if role:
                chunk["role"] = role
            if supabase_file_path:
                chunk["supabase_file_path"] = supabase_file_path
            if supabase_file_url:
                chunk["supabase_file_url"] = supabase_file_url

        # Store each chunk in Supabase database if we have a document_id
        if document_id:
            logger.info(f"Storing {len(document_chunks)} chunks in Supabase for document {document_id}")
            successful_chunks = 0
            failed_chunks = 0

            for i, chunk in enumerate(document_chunks):
                try:
                    # Ensure required fields are present
                    chunk_text = chunk.get("text", "")
                    if not chunk_text.strip():
                        logger.warning(f"Skipping chunk {i} - empty text")
                        failed_chunks += 1
                        continue

                    # Store chunk in Supabase with comprehensive error handling
                    chunk_data = supabase.store_document_chunk(
                        document_id=document_id,
                        chunk_index=chunk.get("chunk_index", i),
                        page_number=chunk.get("page_number", chunk.get("page", 1)),
                        text=chunk_text,
                        embedding=chunk.get("embedding", []),
                        metadata=chunk.get("metadata", {}),
                        source_type=chunk.get("source_type", "document"),
                        chunk_type=chunk.get("chunk_type", "text"),
                        content_type=chunk.get("content_type", "text")
                    )

                    if isinstance(chunk_data, dict) and "error" in chunk_data:
                        logger.error(f"Error storing document chunk {i}: {chunk_data['error']}")
                        failed_chunks += 1
                    else:
                        successful_chunks += 1
                        logger.info(f"Successfully stored document chunk {i+1}/{len(document_chunks)} with ID: {chunk_data.get('id')}")

                except Exception as e:
                    logger.error(f"Exception storing document chunk {i}: {str(e)}")
                    failed_chunks += 1

            logger.info(f"Chunk storage complete: {successful_chunks} successful, {failed_chunks} failed out of {len(document_chunks)} total chunks")

            # If no chunks were stored successfully, this is a critical error
            if successful_chunks == 0:
                logger.error("CRITICAL: No document chunks were stored successfully in Supabase!")
            else:
                logger.info(f"✅ Document upload successful: {successful_chunks} chunks stored in Supabase")

        # Add to global document chunks (legacy in-memory storage)
        DOCUMENT_CHUNKS.extend(document_chunks)

        # Add to vector database for efficient search
        vector_db.add_chunks(document_chunks)

        logger.info(f"Added {len(document_chunks)} document chunks to knowledge base and vector database")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in document_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        result = {
            "message": f"Successfully processed {file.filename}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "document_id": document_id  # Return the document ID if available
        }
        logger.info(f"Document processing successful: {len(chunks_without_embedding)} chunks extracted, document_id: {document_id}")
        logger.info(f"===== DOCUMENT UPLOAD COMPLETED =====")
        return result

    except Exception as e:
        logger.error(f"Error processing uploaded document {file.filename}: {str(e)}")
        # Print more detailed exception information for debugging
        import traceback
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        logger.info(f"===== DOCUMENT UPLOAD FAILED =====")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")
    finally:
        file.file.close()

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    url = request.url
    submitted_by = request.submitted_by
    role = request.role

    logger.info(f"Received request to add website: {url}")

    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")

    try:
        # First, store the website metadata in Supabase
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Store website in Supabase with new category structure
        website_data = supabase.store_website(
            url=url,
            domain=domain,
            title=f"{domain} Website",
            description=f"Content extracted from {url}",
            submitted_by=submitted_by or "system",
            # Use new category IDs if provided, fallback to legacy names
            main_category=request.main_category_id or request.main_category,
            category_level2=request.category_id or request.category,
            sub_category=request.sub_category_id or request.sub_category,
            minor_category=request.minor_category_id or request.minor_category
        )

        if "error" in website_data:
            logger.error(f"Error storing website metadata: {website_data['error']}")
            raise HTTPException(status_code=500, detail=f"Error storing website metadata: {website_data['error']}")

        # Get the website ID
        website_id = website_data.get("id")
        if not website_id:
            logger.error("No website ID returned from Supabase")
            raise HTTPException(status_code=500, detail="No website ID returned from Supabase")

        logger.info(f"Successfully stored website with ID: {website_id}")

        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)

        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")

        # Generate embeddings for each chunk and add website_id
        for i, chunk in enumerate(website_chunks):
            # Add additional metadata
            if submitted_by:
                chunk["submitted_by"] = submitted_by
            if role:
                chunk["role"] = role

            # Add website_id to each chunk (required for vector_db.add_chunks)
            chunk["website_id"] = website_id
            chunk["chunk_index"] = i

            # Add source_type if not present
            if "source_type" not in chunk:
                chunk["source_type"] = "website"

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

            # Add metadata field if not present
            if "metadata" not in chunk:
                chunk["metadata"] = {
                    "url": url,
                    "domain": domain,
                    "extraction_method": chunk.get("extraction_method", "unknown")
                }

        # Add to global document chunks (legacy in-memory storage)
        DOCUMENT_CHUNKS.extend(website_chunks)

        # Store each chunk in Supabase directly
        successful_chunks = 0
        for i, chunk in enumerate(website_chunks):
            # Store chunk in Supabase
            chunk_data = supabase.store_website_chunk(
                website_id=website_id,
                chunk_index=i,
                text=chunk["text"],
                embedding=chunk["embedding"],
                metadata=chunk.get("metadata", {})
            )

            if "error" in chunk_data:
                logger.error(f"Error storing chunk {i}: {chunk_data['error']}")
            else:
                successful_chunks += 1
                logger.info(f"Successfully stored chunk {i} with ID: {chunk_data.get('id')}")

        logger.info(f"Successfully stored {successful_chunks} out of {len(website_chunks)} chunks in Supabase")

        # Add to vector database for efficient search
        vector_db.add_chunks(website_chunks, source_type="website")

        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base and vector database")

        # Log vector database stats
        logger.info(f"Vector database stats: {vector_db.get_stats()}")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        return {
            "message": f"Successfully added website {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "website_id": website_id
        }

    except Exception as e:
        logger.error(f"Error adding website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding website: {str(e)}")

def local_document_search(query_embedding, query_text=None, top_k=20, min_threshold=0.05):
    """
    Local document search using in-memory DOCUMENT_CHUNKS when Supabase is unavailable.
    Uses cosine similarity for vector search.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using local document search with {len(DOCUMENT_CHUNKS)} chunks")

        if not DOCUMENT_CHUNKS:
            logger.warning("No document chunks available for local search")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            # Only process document chunks
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                chunk_copy['source_type'] = 'document'
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        # Return top_k results
        result = scored_chunks[:top_k]
        logger.info(f"Local document search found {len(result)} results (threshold: {min_threshold})")

        return result

    except Exception as e:
        logger.error(f"Error in local document search: {str(e)}")
        return []

def local_website_search(query_embedding, query_text=None, top_k=20, min_threshold=0.4):
    """
    Local website search using in-memory DOCUMENT_CHUNKS when Supabase is unavailable.
    Uses cosine similarity for vector search.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using local website search with {len(DOCUMENT_CHUNKS)} chunks")

        if not DOCUMENT_CHUNKS:
            logger.warning("No document chunks available for local search")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            # Only process website chunks
            if chunk.get('source_type') != 'website':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                chunk_copy['source_type'] = 'website'
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        # Return top_k results
        result = scored_chunks[:top_k]
        logger.info(f"Local website search found {len(result)} results (threshold: {min_threshold})")

        return result

    except Exception as e:
        logger.error(f"Error in local website search: {str(e)}")
        return []

def search_with_fallback(search_func, fallback_func, *args, **kwargs):
    """
    Attempt online search first, fallback to local search if it fails.
    """
    try:
        # Try online search first
        results = search_func(*args, **kwargs)
        if results and len(results) > 0:
            logger.info(f"Online search successful: {len(results)} results")
            return results
        else:
            logger.info("Online search returned no results, trying local fallback")
            return fallback_func(*args, **kwargs)
    except Exception as e:
        logger.warning(f"Online search failed: {str(e)}, trying local fallback")
        return fallback_func(*args, **kwargs)

# Start the server if this is the main module
if __name__ == "__main__":
    import uvicorn
    
    # Log startup information
    logger.info("🚀 [RAILGPT] Starting RailGPT Backend Server...")
    logger.info("🚀 [RAILGPT] Category management router loaded successfully")
    logger.info("🚀 [RAILGPT] Enhanced category management router loaded successfully")
    logger.info("🚀 [RAILGPT] All routes available:")
    
    # Log available routes for debugging
    for route in app.routes:
        if hasattr(route, 'path'):
            logger.info(f"   📍 {route.methods if hasattr(route, 'methods') else ['GET']} {route.path}")
    
    logger.info("🚀 [RAILGPT] Starting server on http://0.0.0.0:8000")
    logger.info("🚀 [RAILGPT] You can use either of these commands:")
    logger.info("🚀 [RAILGPT]   python server.py")
    logger.info("🚀 [RAILGPT]   uvicorn server:app --reload")
    
    # Start the server with uvicorn
    uvicorn.run(
        "server:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        log_level="info"
    )

# Debug endpoint to generate embeddings for all chunks
@app.post("/api/debug/generate-embeddings")
async def debug_generate_embeddings():
    """Debug endpoint to generate embeddings for all chunks in memory."""
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Starting embedding generation for {len(DOCUMENT_CHUNKS)} chunks")

        generated_count = 0
        failed_count = 0

        for i, chunk in enumerate(DOCUMENT_CHUNKS):
            chunk_id = chunk.get('id', f'chunk_{i}')

            # Check if embedding already exists
            if "embedding" in chunk and chunk["embedding"] and len(chunk["embedding"]) > 10:
                continue  # Skip if already has embedding

            # Generate embedding for chunk text
            chunk_text = chunk.get("text", "")
            if chunk_text:
                try:
                    chunk["embedding"] = generate_embedding(chunk_text)
                    generated_count += 1
                    logger.info(f"Generated embedding for chunk {chunk_id} ({generated_count}/{len(DOCUMENT_CHUNKS)})")
                except Exception as e:
                    logger.warning(f"Failed to generate embedding for chunk {chunk_id}: {str(e)}")
                    # Fallback to mock embedding
                    chunk["embedding"] = [0.01] * 768
                    failed_count += 1
            else:
                # No text, use mock embedding
                chunk["embedding"] = [0.01] * 768
                failed_count += 1

        logger.info(f"Embedding generation complete: {generated_count} generated, {failed_count} failed")

        return {
            "message": "Embedding generation completed",
            "total_chunks": len(DOCUMENT_CHUNKS),
            "generated": generated_count,
            "failed": failed_count
        }

    except Exception as e:
        logger.error(f"Error generating embeddings: {str(e)}")
        return {"error": str(e)}

def improved_text_based_document_search(query: str, top_k: int = 10, min_threshold: float = 0.7):
    """
    Improved text-based document search with VERY STRICT relevance requirements.
    Only matches queries that are actually related to railway content.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using STRICT text-based document search for: '{query}' (threshold: {min_threshold})")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Railway domain keywords - MUST have at least one for relevance
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection',
            'indian', 'railways', 'irctc', 'booking', 'ticket', 'metro', 'suburban'
        }

        # Non-railway indicators - immediate disqualification
        non_railway_keywords = {
            'weather', 'temperature', 'climate', 'forecast', 'rain', 'sunny', 'cloudy',
            'movie', 'film', 'actor', 'music', 'song', 'sports', 'football', 'cricket',
            'restaurant', 'food', 'recipe', 'cooking', 'shopping', 'price', 'buy',
            'health', 'medicine', 'doctor', 'hospital', 'disease', 'symptoms',
            'politics', 'government', 'election', 'vote', 'party', 'minister'
        }

        # Check if query contains non-railway keywords - immediate rejection
        non_railway_in_query = non_railway_keywords & query_words
        if non_railway_in_query:
            logger.info(f"Query rejected: contains non-railway keywords {non_railway_in_query}")
            return []

        # Check if query has any railway context
        railway_in_query = railway_keywords & query_words
        if not railway_in_query:
            logger.info(f"Query rejected: no railway keywords found in query")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score with VERY STRICT requirements
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching - require MULTIPLE specific matches
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if len(common_words) >= 2:  # Must have at least 2 words in common
                score += len(common_words) * 0.5

            # 3. Railway domain bonus - but document must also contain railway terms
            chunk_railway_matches = railway_keywords & chunk_words
            if chunk_railway_matches:
                score += len(chunk_railway_matches) * 0.3
            else:
                continue  # Skip if chunk has no railway content

            # 4. Special technical terms
            if 'fsds' in chunk_text:
                score += 1.0
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 0.5

            # 5. Only include if score meets threshold
            if score >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"STRICT text-based document search found {len(result)} relevant chunks (threshold: {min_threshold})")

        # If no results found, log it clearly
        if not result:
            logger.info("No relevant document chunks found - query is not railway-related")

        return result

    except Exception as e:
        logger.error(f"Error in strict text-based document search: {str(e)}")
        return []

def improved_text_based_website_search(query: str, top_k: int = 10, min_threshold: float = 0.7):
    """
    Improved text-based website search with STRICT relevance requirements.
    Only matches queries related to transport/railway content.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using STRICT text-based website search for: '{query}' (threshold: {min_threshold})")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Transport/railway domain keywords - MUST have at least one
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services',
            'ticket', 'booking', 'irctc', 'indian', 'railways'
        }

        # Non-transport indicators - immediate disqualification
        non_transport_keywords = {
            'weather', 'temperature', 'climate', 'forecast', 'rain', 'sunny', 'cloudy',
            'movie', 'film', 'actor', 'music', 'song', 'sports', 'football', 'cricket',
            'restaurant', 'food', 'recipe', 'cooking', 'shopping', 'price', 'buy',
            'health', 'medicine', 'doctor', 'hospital', 'disease', 'symptoms'
        }

        # Check if query contains non-transport keywords - immediate rejection
        non_transport_in_query = non_transport_keywords & query_words
        if non_transport_in_query:
            logger.info(f"Website query rejected: contains non-transport keywords {non_transport_in_query}")
            return []

        # Check if query has any transport context
        transport_in_query = transport_keywords & query_words
        if not transport_in_query:
            logger.info(f"Website query rejected: no transport keywords found in query")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching - require multiple matches
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if len(common_words) >= 2:  # Must have at least 2 words in common
                score += len(common_words) * 0.5

            # 3. Transport domain relevance - chunk must also contain transport terms
            chunk_transport_matches = transport_keywords & chunk_words
            if chunk_transport_matches:
                score += len(chunk_transport_matches) * 0.3
            else:
                continue  # Skip if chunk has no transport content

            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 0.5
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 0.5

            # 5. Only include if score meets threshold
            if score >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"STRICT text-based website search found {len(result)} relevant chunks (threshold: {min_threshold})")

        # If no results found, log it clearly
        if not result:
            logger.info("No relevant website chunks found - query is not transport-related")

        return result

    except Exception as e:
        logger.error(f"Error in strict text-based website search: {str(e)}")
        return []


def get_document_chunks(document_id: str, limit: int = 10):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")

    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []

        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

class CategoryChangeLog(BaseModel):
    document_id: Optional[str] = None
    website_id: Optional[str] = None
    old_main_category: Optional[str] = None
    old_category: Optional[str] = None
    old_sub_category: Optional[str] = None
    old_minor_category: Optional[str] = None
    new_main_category: Optional[str] = None
    new_category: Optional[str] = None
    new_sub_category: Optional[str] = None
    new_minor_category: Optional[str] = None
    changed_by: str
    change_reason: Optional[str] = None

class CategoryReassignmentRequest(BaseModel):
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    changed_by: str = "system"
    change_reason: Optional[str] = None

class BulkCategoryReassignmentRequest(BaseModel):
    item_ids: List[str] = Field(..., min_items=1, description="List of document or website IDs")
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    changed_by: str = "system"
    change_reason: Optional[str] = None

@app.post("/api/documents/{document_id}/reassign-category")
async def reassign_document_category(
    document_id: str,
    reassignment_request: CategoryReassignmentRequest
):
    """Reassign category for a single document with audit trail"""
    try:
        logger.info(f"Reassigning category for document: {document_id}")
        
        # Get current document details for audit trail
        query = supabase.supabase.table('documents').select('*').eq('id', document_id).single()
        result = await run_in_threadpool(query.execute)
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Document not found")
        
        current_doc = result.data
        
        # Prepare update data
        update_data = {}
        if reassignment_request.main_category_id:
            update_data['main_category'] = reassignment_request.main_category_id
        if reassignment_request.category_id:
            update_data['category'] = reassignment_request.category_id
        if reassignment_request.sub_category_id:
            update_data['sub_category'] = reassignment_request.sub_category_id
        if reassignment_request.minor_category_id:
            update_data['minor_category'] = reassignment_request.minor_category_id
        
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        # Update the document
        update_query = supabase.supabase.table('documents').update(update_data).eq('id', document_id)
        update_result = await run_in_threadpool(update_query.execute)
        
        # Create audit trail entry
        audit_data = {
            'id': str(uuid4()),
            'document_id': document_id,
            'old_main_category': current_doc.get('main_category'),
            'old_category': current_doc.get('category'),
            'old_sub_category': current_doc.get('sub_category'),
            'old_minor_category': current_doc.get('minor_category'),
            'new_main_category': reassignment_request.main_category_id,
            'new_category': reassignment_request.category_id,
            'new_sub_category': reassignment_request.sub_category_id,
            'new_minor_category': reassignment_request.minor_category_id,
            'changed_by': reassignment_request.changed_by,
            'change_reason': reassignment_request.change_reason,
            'changed_at': datetime.utcnow().isoformat()
        }
        
        # Try to create audit trail (best effort)
        try:
            audit_query = supabase.supabase.table('category_change_logs').insert(audit_data)
            await run_in_threadpool(audit_query.execute)
            logger.info(f"Created audit trail for document {document_id}")
        except Exception as audit_error:
            logger.warning(f"Failed to create audit trail: {audit_error}")
        
        return {
            "success": True,
            "message": "Document category reassigned successfully",
            "document_id": document_id,
            "updated_data": update_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reassigning document category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/websites/{website_id}/reassign-category")
async def reassign_website_category(
    website_id: str,
    reassignment_request: CategoryReassignmentRequest
):
    """Reassign category for a single website with audit trail"""
    try:
        logger.info(f"Reassigning category for website: {website_id}")
        
        # Get current website details for audit trail
        query = supabase.supabase.table('websites').select('*').eq('id', website_id).single()
        result = await run_in_threadpool(query.execute)
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Website not found")
        
        current_website = result.data
        
        # Prepare update data
        update_data = {}
        if reassignment_request.main_category_id:
            update_data['main_category'] = reassignment_request.main_category_id
        if reassignment_request.category_id:
            update_data['category'] = reassignment_request.category_id
        if reassignment_request.sub_category_id:
            update_data['sub_category'] = reassignment_request.sub_category_id
        if reassignment_request.minor_category_id:
            update_data['minor_category'] = reassignment_request.minor_category_id
        
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        # Update the website
        update_query = supabase.supabase.table('websites').update(update_data).eq('id', website_id)
        update_result = await run_in_threadpool(update_query.execute)
        
        # Create audit trail entry
        audit_data = {
            'id': str(uuid4()),
            'website_id': website_id,
            'old_main_category': current_website.get('main_category'),
            'old_category': current_website.get('category'),
            'old_sub_category': current_website.get('sub_category'),
            'old_minor_category': current_website.get('minor_category'),
            'new_main_category': reassignment_request.main_category_id,
            'new_category': reassignment_request.category_id,
            'new_sub_category': reassignment_request.sub_category_id,
            'new_minor_category': reassignment_request.minor_category_id,
            'changed_by': reassignment_request.changed_by,
            'change_reason': reassignment_request.change_reason,
            'changed_at': datetime.utcnow().isoformat()
        }
        
        # Try to create audit trail (best effort)
        try:
            audit_query = supabase.supabase.table('category_change_logs').insert(audit_data)
            await run_in_threadpool(audit_query.execute)
            logger.info(f"Created audit trail for website {website_id}")
        except Exception as audit_error:
            logger.warning(f"Failed to create audit trail: {audit_error}")
        
        return {
            "success": True,
            "message": "Website category reassigned successfully",
            "website_id": website_id,
            "updated_data": update_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reassigning website category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/documents/bulk-reassign-category")
async def bulk_reassign_document_categories(
    reassignment_request: BulkCategoryReassignmentRequest
):
    """Bulk reassign categories for multiple documents"""
    try:
        logger.info(f"Bulk reassigning categories for {len(reassignment_request.item_ids)} documents")
        
        successful_updates = []
        failed_updates = []
        
        for document_id in reassignment_request.item_ids:
            try:
                # Create individual reassignment request
                individual_request = CategoryReassignmentRequest(
                    main_category_id=reassignment_request.main_category_id,
                    category_id=reassignment_request.category_id,
                    sub_category_id=reassignment_request.sub_category_id,
                    minor_category_id=reassignment_request.minor_category_id,
                    changed_by=reassignment_request.changed_by,
                    change_reason=reassignment_request.change_reason
                )
                
                # Call individual reassignment function
                result = await reassign_document_category(document_id, individual_request)
                successful_updates.append({
                    "document_id": document_id,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"Failed to update document {document_id}: {str(e)}")
                failed_updates.append({
                    "document_id": document_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Bulk reassignment completed: {len(successful_updates)} successful, {len(failed_updates)} failed",
            "successful_updates": successful_updates,
            "failed_updates": failed_updates,
            "total_processed": len(reassignment_request.item_ids)
        }
        
    except Exception as e:
        logger.error(f"Error in bulk document category reassignment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/websites/bulk-reassign-category")
async def bulk_reassign_website_categories(
    reassignment_request: BulkCategoryReassignmentRequest
):
    """Bulk reassign categories for multiple websites"""
    try:
        logger.info(f"Bulk reassigning categories for {len(reassignment_request.item_ids)} websites")
        
        successful_updates = []
        failed_updates = []
        
        for website_id in reassignment_request.item_ids:
            try:
                # Create individual reassignment request
                individual_request = CategoryReassignmentRequest(
                    main_category_id=reassignment_request.main_category_id,
                    category_id=reassignment_request.category_id,
                    sub_category_id=reassignment_request.sub_category_id,
                    minor_category_id=reassignment_request.minor_category_id,
                    changed_by=reassignment_request.changed_by,
                    change_reason=reassignment_request.change_reason
                )
                
                # Call individual reassignment function
                result = await reassign_website_category(website_id, individual_request)
                successful_updates.append({
                    "website_id": website_id,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"Failed to update website {website_id}: {str(e)}")
                failed_updates.append({
                    "website_id": website_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Bulk reassignment completed: {len(successful_updates)} successful, {len(failed_updates)} failed",
            "successful_updates": successful_updates,
            "failed_updates": failed_updates,
            "total_processed": len(reassignment_request.item_ids)
        }
        
    except Exception as e:
        logger.error(f"Error in bulk website category reassignment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/categories/by-level/{entity_type}/{level}")
async def get_categories_by_level(
    entity_type: str,
    level: int,
    parent_id: Optional[str] = Query(None)
):
    """Get categories filtered by entity type, level, and parent"""
    try:
        logger.info(f"Getting categories by level: type={entity_type}, level={level}, parent_id={parent_id}")
        
        # Map level to category type
        level_to_type = {
            1: 'main_category',
            2: 'category', 
            3: 'sub_category',
            4: 'minor_category'
        }
        
        type_filter = level_to_type.get(level)
        if not type_filter:
            raise HTTPException(status_code=400, detail="Invalid level. Must be 1-4")
        
        # Build query
        query = supabase.supabase.table('document_categories').select('*').eq('type', type_filter).eq('is_active', True)
        
        if parent_id:
            query = query.eq('parent_id', parent_id)
        elif level > 1:
            # If no parent specified for non-root level, return empty
            return {"categories": [], "count": 0}
        else:
            # For level 1 (main_category), parent_id should be null
            query = query.is_('parent_id', 'null')
        
        query = query.order('sort_order').order('name')
        result = await run_in_threadpool(query.execute)
        
        if result.data:
            categories = [
                {
                    "id": cat["id"],
                    "name": cat["name"],
                    "type": cat["type"],
                    "parent_id": cat.get("parent_id"),
                    "description": cat.get("description", ""),
                    "full_path": cat.get("full_path", cat["name"]),
                    "level": level
                }
                for cat in result.data
            ]
            logger.info(f"Found {len(categories)} categories for level {level}, parent_id={parent_id}")
        else:
            categories = []
            logger.warning(f"No categories found for level {level}, parent_id={parent_id}")
        
        logger.info(f"Returning {len(categories)} formatted categories")
        return {"categories": categories, "count": len(categories)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting categories by level: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/category-change-logs/{entity_type}/{entity_id}")
async def get_category_change_history(
    entity_type: str,
    entity_id: str
):
    """Get category change history for a document or website"""
    try:
        logger.info(f"Getting category change history for {entity_type}: {entity_id}")
        
        # Build query based on entity type
        if entity_type == "document":
            query = supabase.supabase.table('category_change_logs').select('*').eq('document_id', entity_id)
        elif entity_type == "website":
            query = supabase.supabase.table('category_change_logs').select('*').eq('website_id', entity_id)
        else:
            raise HTTPException(status_code=400, detail="Invalid entity type. Must be 'document' or 'website'")
        
        query = query.order('changed_at', desc=True)
        result = await run_in_threadpool(query.execute)
        
        change_logs = result.data if result.data else []
        
        return {
            "change_logs": change_logs,
            "count": len(change_logs),
            "entity_type": entity_type,
            "entity_id": entity_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting category change history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/debug/re-extract-documents")
async def re_extract_documents():
    """Re-extract chunks for all documents that don't have chunks."""
    try:
        from document_extraction import extract_document
        
        # Get all documents
        documents_query = "SELECT * FROM documents"
        documents = supabase.execute_query(documents_query)
        
        if not documents:
            return {"message": "No documents found", "processed": 0}
        
        processed = 0
        for doc in documents:
            doc_id = doc.get('id')
            file_path = doc.get('file_path', '')
            
            # Check if this document has chunks
            chunks_query = f"SELECT COUNT(*) as count FROM document_chunks WHERE document_id = '{doc_id}'"
            chunks_result = supabase.execute_query(chunks_query)
            
            chunk_count = 0
            if chunks_result and len(chunks_result) > 0:
                chunk_count = chunks_result[0].get('count', 0)
            
            if chunk_count == 0:
                logger.info(f"Re-extracting document: {doc.get('display_name', 'Unknown')}")
                try:
                    # Try to extract from the file path
                    full_path = os.path.join("data", "uploads", os.path.basename(file_path))
                    if os.path.exists(full_path):
                        document_chunks = extract_document(
                            file_path=full_path,
                            uploaded_by=doc.get('uploaded_by', 'system')
                        )
                        logger.info(f"Re-extracted {len(document_chunks)} chunks for {doc.get('display_name')}")
                        processed += 1
                    else:
                        logger.warning(f"File not found: {full_path}")
                except Exception as e:
                    logger.error(f"Error re-extracting {doc.get('display_name')}: {str(e)}")
        
        return {
            "message": f"Re-extraction completed",
            "processed": processed,
            "total_documents": len(documents)
        }
        
    except Exception as e:
        logger.error(f"Error in re-extraction: {str(e)}")
        return {"error": str(e), "processed": 0}

# Add the document content endpoint after the existing API endpoints
@app.get("/api/documents/{document_id}/content")
async def get_document_content(document_id: str):
    """Get document content by ID with proper error handling and file retrieval"""
    logger.info(f"Fetching content for document ID: {document_id}")

    try:
        # First try to get the document info from the database
        document_query = """
        SELECT id, display_name, file_path, file_type, created_at, extracted_content
        FROM documents 
        WHERE id = %s
        """
        
        document_result = supabase.execute_query(document_query, [document_id])
        
        if not document_result or len(document_result) == 0:
            logger.error(f"Document not found in database: {document_id}")
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = document_result[0]
        display_name = document.get('display_name', 'Unknown Document')
        file_path = document.get('file_path', '')
        file_type = document.get('file_type', '')
        extracted_content = document.get('extracted_content', '')
        
        logger.info(f"Document found: {display_name}, file_path: {file_path}")

        # Try different approaches to get the file content
        file_content = None
        content_source = None

        # Method 1: Try to get from Supabase Storage using file_path
        if file_path and file_path.startswith(('documents/', 'data/')):
            try:
                # Clean up the file path for Supabase Storage
                if file_path.startswith('data/uploads/'):
                    storage_path = file_path.replace('data/uploads/', '')
                elif file_path.startswith('documents/'):
                    storage_path = file_path.replace('documents/', '')
                else:
                    storage_path = os.path.basename(file_path)
                
                logger.info(f"Trying to fetch from Supabase Storage: {storage_path}")
                
                # Get file from Supabase Storage
                result = supabase.supabase.storage.from_('documents').download(storage_path)
                
                if result:
                    file_content = result
                    content_source = f"Supabase Storage: {storage_path}"
                    logger.info(f"Successfully retrieved file from Supabase Storage: {storage_path}")
                
            except Exception as storage_error:
                logger.warning(f"Could not fetch from Supabase Storage: {str(storage_error)}")

        # Method 2: Try local file system
        if not file_content:
            # Try various local file paths
            local_paths = [
                file_path,
                os.path.join("data", "uploads", os.path.basename(file_path)),
                os.path.join("backend", "data", "uploads", os.path.basename(file_path)),
                os.path.join("data", "uploads", display_name),
                os.path.basename(file_path)
            ]
            
            for local_path in local_paths:
                if local_path and os.path.exists(local_path):
                    try:
                        with open(local_path, 'rb') as f:
                            file_content = f.read()
                        content_source = f"Local file: {local_path}"
                        logger.info(f"Successfully retrieved file from local path: {local_path}")
                        break
                    except Exception as file_error:
                        logger.warning(f"Could not read local file {local_path}: {str(file_error)}")

        # Method 3: Return extracted content if file not found
        if not file_content and extracted_content:
            logger.info(f"Returning extracted content for document: {display_name}")
            return {
                "content": extracted_content,
                "content_type": "text/plain",
                "source": "extracted_content",
                "document_name": display_name,
                "document_id": document_id
            }

        # Method 4: Generate meaningful content if nothing found
        if not file_content:
            logger.warning(f"No file content found for document: {display_name}")
            # Generate helpful error content
            error_content = f"""# Document Content Not Available

**Document:** {display_name}
**Document ID:** {document_id}
**File Type:** {file_type}

The original file could not be retrieved from storage. This may be due to:

1. File was not properly uploaded to Supabase Storage
2. File path in database is incorrect
3. File was moved or deleted

Please try re-uploading the document or contact support if this issue persists.

**Debug Information:**
- File Path: {file_path}
- Storage attempts made: Yes
- Local file attempts made: Yes
- Extracted content available: {'Yes' if extracted_content else 'No'}
"""
            return {
                "content": error_content,
                "content_type": "text/markdown",
                "source": "error_message",
                "document_name": display_name,
                "document_id": document_id,
                "error": "File content not available"
            }

        # Return the file content
        from fastapi.responses import Response
        
        # Determine content type
        content_type = "application/octet-stream"
        if file_type:
            content_type_map = {
                'pdf': 'application/pdf',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'txt': 'text/plain',
                'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'xls': 'application/vnd.ms-excel'
            }
            content_type = content_type_map.get(file_type.lower(), content_type)

        logger.info(f"Returning file content - Size: {len(file_content)} bytes, Type: {content_type}, Source: {content_source}")
        
        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f'inline; filename="{display_name}"',
                "X-Content-Source": content_source or "unknown"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving document content for {document_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error serving document content: {str(e)}"
        )

# Document viewer endpoint
@app.get("/api/documents/view/{filename}")
async def view_document(filename: str):
    """View document by filename with fallback to document ID lookup"""
    try:
        from urllib.parse import unquote
        decoded_filename = unquote(filename)
        logger.info(f"Viewing document: {decoded_filename}")

        # First try to find document by filename
        document_query = """
        SELECT id, display_name, file_path, file_type 
        FROM documents 
        WHERE display_name = %s OR filename = %s
        ORDER BY created_at DESC
        LIMIT 1
        """
        
        document_result = supabase.execute_query(document_query, [decoded_filename, decoded_filename])
        
        if document_result and len(document_result) > 0:
            document = document_result[0]
            document_id = document['id']
            logger.info(f"Found document by filename: {document_id}")
            
            # Redirect to the content endpoint
            return await get_document_content(document_id)
        else:
            # Try to find by partial filename match
            like_query = """
            SELECT id, display_name, file_path, file_type 
            FROM documents 
            WHERE display_name ILIKE %s OR filename ILIKE %s
            ORDER BY created_at DESC
            LIMIT 1
            """
            
            like_pattern = f"%{decoded_filename}%"
            document_result = supabase.execute_query(like_query, [like_pattern, like_pattern])
            
            if document_result and len(document_result) > 0:
                document = document_result[0]
                document_id = document['id']
                logger.info(f"Found document by partial match: {document_id}")
                
                return await get_document_content(document_id)
            else:
                logger.error(f"Document not found: {decoded_filename}")
                raise HTTPException(
                    status_code=404,
                    detail=f"Document '{decoded_filename}' not found"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error viewing document: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error viewing document: {str(e)}"
        )

# Add this before the main if __name__ == "__main__" block at the end of the file

@app.get("/api/debug/search-test")
async def debug_search_test(query: str = "railway"):
    """Comprehensive debug endpoint to test all search functionality"""
    logger.info(f"🔬 [DEBUG-SEARCH-TEST] Testing search functionality with query: '{query}'")
    
    debug_results = {
        "query": query,
        "embedding_generation": {},
        "document_search": {},
        "website_search": {},
        "system_status": {}
    }
    
    try:
        # Test 1: Embedding Generation
        logger.info("🔬 [TEST 1] Testing embedding generation...")
        try:
            query_embedding = generate_embedding(query, "gemini-2.0-flash")
            debug_results["embedding_generation"] = {
                "status": "success",
                "embedding_size": len(query_embedding) if query_embedding else 0,
                "embedding_preview": query_embedding[:5] if query_embedding else None
            }
            logger.info(f"✅ [TEST 1] Embedding generated: size {len(query_embedding) if query_embedding else 0}")
        except Exception as e:
            debug_results["embedding_generation"] = {
                "status": "failed",
                "error": str(e)
            }
            logger.error(f"❌ [TEST 1] Embedding generation failed: {str(e)}")
            query_embedding = None
        
        # Test 2: Document Chunk Search
        logger.info("🔬 [TEST 2] Testing document chunk search...")
        try:
            # Try enhanced search first
            doc_chunks_enhanced = search_supabase_document_chunks_enhanced(
                query_embedding=query_embedding,
                query_text=query,
                use_hybrid_search=True,
                top_k=10,
                min_threshold=0.4
            )
            
            # Try regular search
            doc_chunks_regular = search_supabase_document_chunks(
                query_embedding=query_embedding,
                query_text=query,
                use_hybrid_search=True,
                top_k=10,
                min_threshold=0.4
            )
            
            # Try text-based search
            doc_chunks_text = search_documents_by_content(query, limit=10)
            
            debug_results["document_search"] = {
                "enhanced_search": {
                    "status": "success",
                    "chunks_found": len(doc_chunks_enhanced) if doc_chunks_enhanced else 0,
                    "top_similarities": [chunk.get('similarity', 0) for chunk in (doc_chunks_enhanced[:3] if doc_chunks_enhanced else [])]
                },
                "regular_search": {
                    "status": "success", 
                    "chunks_found": len(doc_chunks_regular) if doc_chunks_regular else 0,
                    "top_similarities": [chunk.get('similarity', 0) for chunk in (doc_chunks_regular[:3] if doc_chunks_regular else [])]
                },
                "text_search": {
                    "status": "success",
                    "chunks_found": len(doc_chunks_text) if doc_chunks_text else 0
                }
            }
            
            logger.info(f"✅ [TEST 2] Document search completed:")
            logger.info(f"   Enhanced: {len(doc_chunks_enhanced) if doc_chunks_enhanced else 0} chunks")
            logger.info(f"   Regular: {len(doc_chunks_regular) if doc_chunks_regular else 0} chunks") 
            logger.info(f"   Text: {len(doc_chunks_text) if doc_chunks_text else 0} chunks")
            
        except Exception as e:
            debug_results["document_search"] = {
                "status": "failed",
                "error": str(e)
            }
            logger.error(f"❌ [TEST 2] Document search failed: {str(e)}")
        
        # Test 3: Website Chunk Search
        logger.info("🔬 [TEST 3] Testing website chunk search...")
        try:
            # Try vector search
            web_chunks_vector = search_supabase_website_chunks(
                query_embedding=query_embedding,
                query_text=query,
                use_hybrid_search=True,
                top_k=10,
                min_threshold=0.4
            )
            
            # Try text-based search
            web_chunks_text = text_based_website_search(query, top_k=10)
            
            debug_results["website_search"] = {
                "vector_search": {
                    "status": "success",
                    "chunks_found": len(web_chunks_vector) if web_chunks_vector else 0,
                    "top_similarities": [chunk.get('similarity', 0) for chunk in (web_chunks_vector[:3] if web_chunks_vector else [])]
                },
                "text_search": {
                    "status": "success",
                    "chunks_found": len(web_chunks_text) if web_chunks_text else 0
                }
            }
            
            logger.info(f"✅ [TEST 3] Website search completed:")
            logger.info(f"   Vector: {len(web_chunks_vector) if web_chunks_vector else 0} chunks")
            logger.info(f"   Text: {len(web_chunks_text) if web_chunks_text else 0} chunks")
            
        except Exception as e:
            debug_results["website_search"] = {
                "status": "failed", 
                "error": str(e)
            }
            logger.error(f"❌ [TEST 3] Website search failed: {str(e)}")
        
        # Test 4: System Status
        logger.info("🔬 [TEST 4] Checking system status...")
        try:
            # Check total chunks available
            total_doc_chunks = len(DOCUMENT_CHUNKS) if 'DOCUMENT_CHUNKS' in globals() else 0
            
            # Check database connection
            from supabase_client import supabase
            doc_count_query = "SELECT COUNT(*) as count FROM document_chunks"
            doc_count_result = supabase.execute_query(doc_count_query)
            db_doc_chunks = doc_count_result[0]['count'] if doc_count_result else 0
            
            website_count_query = "SELECT COUNT(*) as count FROM website_chunks"
            website_count_result = supabase.execute_query(website_count_query)
            db_website_chunks = website_count_result[0]['count'] if website_count_result else 0
            
            debug_results["system_status"] = {
                "status": "success",
                "local_document_chunks": total_doc_chunks,
                "database_document_chunks": db_doc_chunks,
                "database_website_chunks": db_website_chunks,
                "supabase_connection": "working"
            }
            
            logger.info(f"✅ [TEST 4] System status checked:")
            logger.info(f"   Local doc chunks: {total_doc_chunks}")
            logger.info(f"   DB doc chunks: {db_doc_chunks}")
            logger.info(f"   DB website chunks: {db_website_chunks}")
            
        except Exception as e:
            debug_results["system_status"] = {
                "status": "failed",
                "error": str(e)
            }
            logger.error(f"❌ [TEST 4] System status check failed: {str(e)}")
        
        # Final summary
        logger.info("🔬 [DEBUG-SEARCH-TEST] Test completed successfully")
        debug_results["overall_status"] = "completed"
        
        return debug_results
        
    except Exception as e:
        logger.error(f"❌ [DEBUG-SEARCH-TEST] Critical error: {str(e)}")
        debug_results["overall_status"] = "failed"
        debug_results["error"] = str(e)
        return debug_results

@app.get("/api/debug/vector-status")
async def debug_vector_status():
    """Check the status of vector search components"""
    logger.info("🔬 [DEBUG-VECTOR-STATUS] Checking vector search status...")
    
    status = {
        "vector_search": {},
        "embeddings": {},
        "database": {},
        "models": {}
    }
    
    try:
        # Check embedding function
        try:
            test_embedding = generate_embedding("test query", "gemini-2.0-flash")
            status["embeddings"] = {
                "status": "working",
                "model": "gemini-2.0-flash", 
                "embedding_size": len(test_embedding) if test_embedding else 0
            }
        except Exception as e:
            status["embeddings"] = {
                "status": "failed",
                "error": str(e)
            }
        
        # Check database vector functions
        try:
            from supabase_client import supabase
            
            # Check if vector functions exist
            vector_functions_query = """
            SELECT proname 
            FROM pg_proc 
            WHERE proname IN ('search_document_chunks', 'search_website_chunks')
            """
            functions_result = supabase.execute_query(vector_functions_query)
            
            status["database"] = {
                "status": "working",
                "vector_functions_available": len(functions_result) if functions_result else 0,
                "functions": [f['proname'] for f in functions_result] if functions_result else []
            }
        except Exception as e:
            status["database"] = {
                "status": "failed",
                "error": str(e)
            }
        
        # Check LLM router status
        try:
            available_models = llm_router.get_available_models() if hasattr(llm_router, 'get_available_models') else []
            status["models"] = {
                "status": "working",
                "available_models": available_models,
                "default_model": llm_router.DEFAULT_MODEL if hasattr(llm_router, 'DEFAULT_MODEL') else "unknown"
            }
        except Exception as e:
            status["models"] = {
                "status": "failed",
                "error": str(e)
            }
        
        logger.info("✅ [DEBUG-VECTOR-STATUS] Vector status check completed")
        return status
        
    except Exception as e:
        logger.error(f"❌ [DEBUG-VECTOR-STATUS] Error: {str(e)}")
        return {"error": str(e), "status": "failed"}


# Startup configuration to support both python server.py and uvicorn commands
if __name__ == "__main__":
    import uvicorn
    
    # Log startup information
    logger.info("🚀 [RAILGPT] Starting RailGPT Backend Server...")
    logger.info("🚀 [RAILGPT] Category management router loaded successfully")
    logger.info("🚀 [RAILGPT] Enhanced category management router loaded successfully")
    logger.info("🚀 [RAILGPT] All routes available:")
    
    # Log available routes for debugging
    for route in app.routes:
        if hasattr(route, 'path'):
            logger.info(f"   📍 {route.methods if hasattr(route, 'methods') else ['GET']} {route.path}")
    
    logger.info("🚀 [RAILGPT] Starting server on http://0.0.0.0:8000")
    logger.info("🚀 [RAILGPT] You can use either of these commands:")
    logger.info("🚀 [RAILGPT]   python server.py")
    logger.info("🚀 [RAILGPT]   uvicorn server:app --reload")
    
    # Start the server with uvicorn
    uvicorn.run(
        "server:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        log_level="info"
    )

