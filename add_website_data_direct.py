#!/usr/bin/env python3
"""
Add website data directly to the database to test the system.
"""

import os
import sys
import json
import uuid
from datetime import datetime

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), 'backend', '.env'))

try:
    from supabase_client import supabase
    import llm_router
    print("✅ Modules imported successfully")
except Exception as e:
    print(f"❌ Error importing modules: {str(e)}")
    sys.exit(1)

def add_rapidresponse_website():
    """Add rapidresponseapp.com website and chunks to database."""
    print("🌐 Adding rapidresponseapp.com website...")
    
    try:
        # First, add the website
        website_data = {
            "id": str(uuid.uuid4()),
            "url": "https://rapidresponseapp.com",
            "name": "Rapid Response App",
            "title": "Emergency Response System for Transportation",
            "status": "processed",
            "domain": "rapidresponseapp.com",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        # Insert website
        website_result = supabase.table("websites").insert(website_data).execute()
        if hasattr(website_result, 'data') and website_result.data:
            website_id = website_result.data[0]['id']
            print(f"   ✅ Added website with ID: {website_id}")
            
            # Website content chunks
            website_chunks = [
                {
                    "text": "Rapid Response App is a cutting-edge emergency response application designed for transportation safety. The app provides real-time incident reporting, emergency coordination, and rapid response capabilities for public transportation systems including railways, buses, and metro systems.",
                    "url": "https://rapidresponseapp.com",
                    "title": "Rapid Response App - Emergency Response System"
                },
                {
                    "text": "Our rapid response app features include: 1) Real-time emergency alerts, 2) GPS-based incident location tracking, 3) Multi-agency coordination tools, 4) Public safety notifications, 5) Transportation safety monitoring, 6) Emergency response team dispatch, 7) Incident documentation and reporting.",
                    "url": "https://rapidresponseapp.com/features",
                    "title": "Rapid Response App Features"
                },
                {
                    "text": "The rapid response app specializes in transportation safety for railways, public transit, and emergency services. Our platform enables quick response to transportation incidents, safety hazards, and emergency situations across all modes of public transport.",
                    "url": "https://rapidresponseapp.com/transportation",
                    "title": "Transportation Safety Solutions"
                }
            ]
            
            # Add website chunks
            for i, chunk_content in enumerate(website_chunks):
                try:
                    # Generate embedding
                    embedding = llm_router.generate_embedding(chunk_content['text'])
                    
                    chunk_data = {
                        "id": str(uuid.uuid4()),
                        "website_id": website_id,
                        "chunk_index": i,
                        "text": chunk_content['text'],
                        "embedding": json.dumps(embedding),
                        "url": chunk_content['url'],
                        "source_type": "website",
                        "metadata": {
                            "title": chunk_content['title'],
                            "url": chunk_content['url'],
                            "domain": "rapidresponseapp.com"
                        },
                        "created_at": datetime.utcnow().isoformat()
                    }
                    
                    chunk_result = supabase.table("website_chunks").insert(chunk_data).execute()
                    if hasattr(chunk_result, 'data') and chunk_result.data:
                        print(f"   ✅ Added website chunk {i+1}: {chunk_content['title']}")
                    else:
                        print(f"   ❌ Failed to add website chunk {i+1}")
                        
                except Exception as e:
                    print(f"   ❌ Error adding chunk {i+1}: {str(e)}")
            
            return True
        else:
            print("   ❌ Failed to add website")
            return False
            
    except Exception as e:
        print(f"   ❌ Error adding website: {str(e)}")
        return False

def check_database_content():
    """Check what's in the database."""
    print("\n📊 Checking database content...")
    
    try:
        # Check websites
        websites = supabase.table("websites").select("*").execute()
        website_count = len(websites.data) if hasattr(websites, 'data') and websites.data else 0
        print(f"   Websites: {website_count}")
        
        # Check website chunks
        chunks = supabase.table("website_chunks").select("*").execute()
        chunk_count = len(chunks.data) if hasattr(chunks, 'data') and chunks.data else 0
        print(f"   Website chunks: {chunk_count}")
        
        # Check documents
        docs = supabase.table("documents").select("*").execute()
        doc_count = len(docs.data) if hasattr(docs, 'data') and docs.data else 0
        print(f"   Documents: {doc_count}")
        
        # Check document chunks
        doc_chunks = supabase.table("document_chunks").select("*").execute()
        doc_chunk_count = len(doc_chunks.data) if hasattr(doc_chunks, 'data') and doc_chunks.data else 0
        print(f"   Document chunks: {doc_chunk_count}")
        
        return {
            "websites": website_count,
            "website_chunks": chunk_count,
            "documents": doc_count,
            "document_chunks": doc_chunk_count
        }
        
    except Exception as e:
        print(f"   ❌ Error checking database: {str(e)}")
        return {}

def main():
    """Main function."""
    print("🔧 Adding Website Data Directly to Database")
    print("=" * 50)
    
    # Check current content
    initial_content = check_database_content()
    
    # Add website content
    success = add_rapidresponse_website()
    
    # Check content after addition
    final_content = check_database_content()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print(f"   Website addition: {'✅ SUCCESS' if success else '❌ FAILED'}")
    print(f"   Website chunks added: {final_content.get('website_chunks', 0) - initial_content.get('website_chunks', 0)}")
    
    if success:
        print("\n🎉 Website data added successfully!")
        print("💡 Now you can test queries about 'rapid response app'")
    else:
        print("\n⚠️ Failed to add website data")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
