#!/usr/bin/env python
"""Debug script to check what's in the database."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from supabase_client import supabase

print("🔍 CHECKING DATABASE CONTENTS")
print("=" * 50)

# Check document counts
try:
    doc_count_query = "SELECT COUNT(*) as count FROM documents"
    doc_count = supabase.execute_query(doc_count_query)
    print(f"📄 Total documents: {doc_count[0]['count'] if doc_count else 'Error'}")
    
    # Sample document names
    doc_sample_query = "SELECT COALESCE(display_name, file_name, name) as filename FROM documents LIMIT 5"
    doc_samples = supabase.execute_query(doc_sample_query)
    print("📄 Sample documents:")
    for doc in (doc_samples or []):
        print(f"   - {doc.get('filename', 'Unknown')}")
        
except Exception as e:
    print(f"❌ Document query error: {str(e)}")

# Check website counts  
try:
    web_count_query = "SELECT COUNT(*) as count FROM websites"
    web_count = supabase.execute_query(web_count_query)
    print(f"\n🌐 Total websites: {web_count[0]['count'] if web_count else 'Error'}")
    
    # Sample website URLs
    web_sample_query = "SELECT url, title FROM websites LIMIT 5"
    web_samples = supabase.execute_query(web_sample_query)
    print("🌐 Sample websites:")
    for web in (web_samples or []):
        print(f"   - {web.get('title', 'No title')}: {web.get('url', 'No URL')}")
        
except Exception as e:
    print(f"❌ Website query error: {str(e)}")

# Check document chunks
try:
    chunk_count_query = "SELECT COUNT(*) as count FROM document_chunks"
    chunk_count = supabase.execute_query(chunk_count_query)
    print(f"\n📝 Total document chunks: {chunk_count[0]['count'] if chunk_count else 'Error'}")
    
    # Sample chunk with ACP
    acp_chunk_query = """
    SELECT dc.text, d.display_name as filename 
    FROM document_chunks dc 
    JOIN documents d ON dc.document_id = d.id 
    WHERE dc.text ILIKE '%ACP%' 
    LIMIT 3
    """
    acp_chunks = supabase.execute_query(acp_chunk_query)
    print("📝 Sample chunks mentioning ACP:")
    for chunk in (acp_chunks or []):
        text_preview = chunk.get('text', '')[:100] + "..." if len(chunk.get('text', '')) > 100 else chunk.get('text', '')
        print(f"   - {chunk.get('filename', 'Unknown')}: {text_preview}")
        
except Exception as e:
    print(f"❌ Chunk query error: {str(e)}")

# Check website chunks
try:
    web_chunk_count_query = "SELECT COUNT(*) as count FROM website_chunks"
    web_chunk_count = supabase.execute_query(web_chunk_count_query)
    print(f"\n🌐 Total website chunks: {web_chunk_count[0]['count'] if web_chunk_count else 'Error'}")
    
except Exception as e:
    print(f"❌ Website chunk query error: {str(e)}")

print("\n✅ Database check completed")
