import requests
import json

def test_endpoints():
    """Test the fixed endpoints to ensure count query errors are resolved."""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing Fixed Endpoints...")
    print("=" * 50)
    
    # Test health endpoint first
    try:
        response = requests.get(f"{base_url}/api/health")
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Get websites to test with
    try:
        response = requests.get(f"{base_url}/api/websites")
        if response.status_code == 200:
            websites = response.json()
            print(f"📋 Found {len(websites)} websites in database")
            
            if websites:
                website_id = websites[0]['id']
                print(f"🔍 Testing website ID: {website_id}")
                
                # Test website extraction details (the problematic endpoint)
                try:
                    response = requests.get(f"{base_url}/api/websites/{website_id}/extraction-details")
                    if response.status_code == 200:
                        data = response.json()
                        print("✅ Website extraction details endpoint working")
                        print(f"   - Chunks count: {data.get('chunks', 'N/A')}")
                        print(f"   - URL: {data.get('website_url', 'N/A')}")
                    else:
                        print(f"❌ Website extraction details failed: {response.status_code}")
                        print(f"   Response: {response.text}")
                except Exception as e:
                    print(f"❌ Website extraction details error: {e}")
                
                # Test website content endpoint
                try:
                    response = requests.get(f"{base_url}/api/websites/{website_id}/content")
                    if response.status_code == 200:
                        data = response.json()
                        print("✅ Website content endpoint working")
                        print(f"   - Content length: {len(data.get('content', ''))}")
                    else:
                        print(f"❌ Website content failed: {response.status_code}")
                        print(f"   Response: {response.text}")
                except Exception as e:
                    print(f"❌ Website content error: {e}")
                    
            else:
                print("⚠️  No websites found to test with")
        else:
            print(f"❌ Failed to get websites: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting websites: {e}")
    
    # Get documents to test with
    try:
        response = requests.get(f"{base_url}/api/documents")
        if response.status_code == 200:
            documents = response.json()
            print(f"📋 Found {len(documents)} documents in database")
            
            if documents:
                document_id = documents[0]['id']
                print(f"🔍 Testing document ID: {document_id}")
                
                # Test document extraction details
                try:
                    response = requests.get(f"{base_url}/api/documents/{document_id}/extraction-details")
                    if response.status_code == 200:
                        data = response.json()
                        print("✅ Document extraction details endpoint working")
                        print(f"   - Chunks count: {data.get('chunks', 'N/A')}")
                        print(f"   - Document: {data.get('document_name', 'N/A')}")
                    else:
                        print(f"❌ Document extraction details failed: {response.status_code}")
                        print(f"   Response: {response.text}")
                except Exception as e:
                    print(f"❌ Document extraction details error: {e}")
                
                # Test document content endpoint
                try:
                    response = requests.get(f"{base_url}/api/documents/{document_id}/content")
                    if response.status_code == 200:
                        data = response.json()
                        print("✅ Document content endpoint working")
                        print(f"   - Content length: {len(data.get('content', ''))}")
                    else:
                        print(f"❌ Document content failed: {response.status_code}")
                        print(f"   Response: {response.text}")
                except Exception as e:
                    print(f"❌ Document content error: {e}")
                    
            else:
                print("⚠️  No documents found to test with")
        else:
            print(f"❌ Failed to get documents: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting documents: {e}")
    
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    test_endpoints() 