#!/usr/bin/env python3
"""
Add website content to the database for testing.
"""

import os
import sys
import json
import requests

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def add_website_via_api():
    """Add website content using the API endpoint."""
    print("🌐 Adding website content via API...")
    
    try:
        # Add rapidresponseapp.com
        website_data = {
            "url": "https://rapidresponseapp.com",
            "submitted_by": "system",
            "role": "admin",
            "follow_links": False,
            "extraction_depth": 1,
            "extract_images": False,
            "extract_tables": True,
            "max_pages": 5,
            "extractor_type": "trafilatura",
            "domain_category": "transportation"
        }
        
        response = requests.post(
            "http://localhost:8000/api/websites/add",
            json=website_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Website added successfully: {result}")
            return True
        else:
            print(f"   ❌ Failed to add website: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error adding website: {str(e)}")
        return False

def test_simple_query():
    """Test a simple query to see if the system is working."""
    print("\n🔍 Testing simple query...")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"query": "What is ACP?"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Query successful")
            print(f"   📄 Document sources: {len(result.get('document_sources', []))}")
            print(f"   🌐 Website sources: {len(result.get('website_sources', []))}")
            print(f"   🤖 LLM fallback: {result.get('llm_fallback_used', False)}")
            print(f"   💬 Answer preview: {result.get('answer', '')[:100]}...")
            return True
        else:
            print(f"   ❌ Query failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Query error: {str(e)}")
        return False

def main():
    """Main function."""
    print("🔧 Adding Website Content and Testing")
    print("=" * 40)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not healthy")
            return False
    except:
        print("❌ Server is not running")
        return False
    
    print("✅ Server is running")
    
    # Add website content
    website_added = add_website_via_api()
    
    # Test query
    query_works = test_simple_query()
    
    if website_added and query_works:
        print("\n🎉 System is working!")
        return True
    else:
        print("\n⚠️ Some issues detected")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
