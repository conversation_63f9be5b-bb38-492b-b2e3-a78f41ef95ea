#!/usr/bin/env python3
"""
Simple test to check if the RailGPT server is responding and can handle basic queries.
"""

import requests
import json
import time

def test_server():
    """Test basic server functionality."""
    base_url = "http://localhost:8000"
    
    print("🔍 Testing RailGPT Server...")
    
    # Test 1: Health check
    try:
        print("1. Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Health check passed")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Health check failed: {str(e)}")
        return False
    
    # Test 2: Basic query
    try:
        print("2. Testing basic query...")
        query_data = {"query": "What is ACP?"}
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ Query successful")
            print(f"   Answer length: {len(result.get('answer', ''))}")
            print(f"   Sources: {len(result.get('sources', []))}")
            print(f"   LLM fallback: {result.get('llm_fallback_used', False)}")
            
            # Print first 100 chars of answer
            answer = result.get('answer', '')
            if answer:
                print(f"   Answer preview: {answer[:100]}...")
            
            return True
        else:
            print(f"   ❌ Query failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Query failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_server()
    if success:
        print("\n🎉 Server is working!")
    else:
        print("\n❌ Server has issues")
