{"website_added": false, "test_results": [{"success": true, "query": "What is the full form of ACP?", "expected": "document", "actual": "document", "matches_expectation": true, "document_sources": 1, "website_sources": 0, "llm_fallback_used": false, "answer_length": 241, "answer_preview": "Based on the available document information, I cannot determine the full form of \"ACP\". The provided...", "status": "PASSED", "test_name": "ACP Full Form (Documents Only)"}, {"success": false, "error": "HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "query": "Tell me about rapid response app", "status": "FAILED", "test_name": "Rapid Response App (Websites Only)"}, {"success": false, "error": "HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "query": "What is VASP development?", "status": "FAILED", "test_name": "VASP Development (Both Sources)"}, {"success": false, "error": "HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "query": "What are WDG4G details?", "status": "FAILED", "test_name": "WDG4G Details (LLM Fallback)"}], "all_tests_passed": false, "summary": {"total_tests": 4, "passed": 1, "failed": 3}}