# Document Images Directory

This directory stores document images referenced by the application.

## Image Naming Convention

Images should follow this naming pattern:
- `documentname_pageN.png` - For document page images (e.g., `acp_110v_page1.png`)
- `logo_name.png` - For logos and icons

## Usage

The application will look for images in this directory when they cannot be loaded from the original source URL or base64 data.

This provides a reliable fallback mechanism when images can't be loaded through their primary paths. 