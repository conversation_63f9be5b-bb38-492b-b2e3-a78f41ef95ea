{"ast": null, "code": "// Supabase client for frontend\nimport supabaseClient,{checkSupabaseConnection}from'../utils/supabaseClient';// Export the client for use throughout the application\nexport const supabase=supabaseClient;// Export the connection check function\nexport const testSupabaseConnection=checkSupabaseConnection;// Document types\n// Website types\n// Query types\n// Document operations\nexport const getDocuments=async()=>{try{const{data,error}=await supabase.from('documents').select('*').order('created_at',{ascending:false});if(error){console.error('Error fetching documents:',error);return[];}return data||[];}catch(error){console.error('Error in getDocuments:',error);return[];}};export const getDocumentById=async id=>{try{const{data,error}=await supabase.from('documents').select('*').eq('id',id).single();if(error){console.error('Error fetching document:',error);return null;}return data;}catch(error){console.error('Error in getDocumentById:',error);return null;}};export const getDocumentChunks=async documentId=>{try{const{data,error}=await supabase.from('document_chunks').select('*').eq('document_id',documentId).order('chunk_index',{ascending:true});if(error){console.error('Error fetching document chunks:',error);return[];}return data||[];}catch(error){console.error('Error in getDocumentChunks:',error);return[];}};// Website operations\nexport const getWebsites=async()=>{try{const{data,error}=await supabase.from('websites').select('*').order('created_at',{ascending:false});if(error){console.error('Error fetching websites:',error);return[];}return data||[];}catch(error){console.error('Error in getWebsites:',error);return[];}};export const getWebsiteById=async id=>{try{const{data,error}=await supabase.from('websites').select('*').eq('id',id).single();if(error){console.error('Error fetching website:',error);return null;}return data;}catch(error){console.error('Error in getWebsiteById:',error);return null;}};// Query operations\nexport const saveQuery=async query=>{try{const{data,error}=await supabase.from('queries').insert([query]).select().single();if(error){console.error('Error saving query:',error);return null;}return data;}catch(error){console.error('Error in saveQuery:',error);return null;}};export const getRecentQueries=async function(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;try{const{data,error}=await supabase.from('queries').select('*').order('created_at',{ascending:false}).limit(limit);if(error){console.error('Error fetching recent queries:',error);return[];}return data||[];}catch(error){console.error('Error in getRecentQueries:',error);return[];}};// Storage operations\nexport const uploadFile=async(filePath,file,onProgress)=>{try{console.log(`🔄 Starting file upload: ${filePath}`);console.log(`📁 File details: ${file.name}, ${file.size} bytes, ${file.type}`);// Try multiple upload strategies to handle RLS issues\n// Strategy 1: Direct upload with auth context\ntry{const{data,error}=await supabase.storage.from('documents').upload(filePath,file,{cacheControl:'3600',upsert:true,// Allow overwriting\ncontentType:file.type||'application/octet-stream'});if(error){console.warn(`⚠️ Strategy 1 failed:`,error);throw error;}// Get public URL\nconst{data:{publicUrl}}=supabase.storage.from('documents').getPublicUrl(data.path);console.log(`✅ Strategy 1 success: ${publicUrl}`);return publicUrl;}catch(strategy1Error){console.warn(`Strategy 1 failed, trying Strategy 2:`,strategy1Error);// Strategy 2: Upload with simplified path and anonymous context\ntry{// Create simpler file path\nconst simplePath=`${Date.now()}_${file.name}`;const{data,error}=await supabase.storage.from('documents').upload(simplePath,file,{upsert:true,contentType:file.type});if(error){console.warn(`⚠️ Strategy 2 failed:`,error);throw error;}const{data:{publicUrl}}=supabase.storage.from('documents').getPublicUrl(data.path);console.log(`✅ Strategy 2 success: ${publicUrl}`);return publicUrl;}catch(strategy2Error){console.warn(`Strategy 2 failed, trying Strategy 3:`,strategy2Error);// Strategy 3: Use backend upload endpoint as fallback\ntry{const formData=new FormData();formData.append('file',file);formData.append('uploaded_by','frontend_user');const response=await fetch('/api/upload-document',{method:'POST',body:formData});if(!response.ok){throw new Error(`Backend upload failed: ${response.statusText}`);}const result=await response.json();console.log(`✅ Strategy 3 success via backend:`,result);return result.storage_url||result.local_path||'uploaded_via_backend';}catch(strategy3Error){console.error(`❌ All upload strategies failed:`,{strategy1:strategy1Error,strategy2:strategy2Error,strategy3:strategy3Error});// Return null to indicate failure\nreturn null;}}}}catch(error){console.error('❌ Critical upload error:',error);return null;}};export const getFileUrl=filePath=>{const{data:{publicUrl}}=supabase.storage.from('documents').getPublicUrl(filePath);return publicUrl;};// Chat Session types\n// Chat Session operations\nexport const createChatSession=async function(){let title=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'New Chat';let modelUsed=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'gemini-2.0-flash';try{const{data,error}=await supabase.from('chat_sessions').insert([{title,messages:[],model_used:modelUsed,has_document:false,has_website:false}]).select().single();if(error){console.error('Error creating chat session:',error);return null;}return{...data,messages:data.messages||[]};}catch(error){console.error('Error in createChatSession:',error);return null;}};export const getChatSessions=async userId=>{try{console.log('Attempting to fetch chat sessions...');// First, try to check if the table exists with a simple query\nconst{data:tableCheck,error:tableError}=await supabase.from('chat_sessions').select('id').limit(1);if(tableError){console.warn('Chat sessions table not accessible:',tableError.message);console.log('Returning empty chat sessions array');return[];}// If table exists, try the full query with basic columns only\nlet query=supabase.from('chat_sessions').select('id, title, created_at, updated_at').order('updated_at',{ascending:false}).limit(10);// Reduced limit\nif(userId){query=query.eq('user_id',userId);}const{data,error}=await query;if(error){console.warn('Chat sessions query failed:',error.message);return[];}// Process and return data with safe defaults\nconst sessions=(data||[]).map(session=>({id:session.id,title:session.title||'Untitled Chat',created_at:session.created_at,updated_at:session.updated_at,model_used:'gemini-2.0-flash',has_document:false,has_website:false,messages:[]}));console.log(`Successfully loaded ${sessions.length} chat sessions`);return sessions;}catch(error){console.warn('Error in getChatSessions:',error.message||error);return[];}};export const getChatSessionById=async id=>{try{const{data,error}=await supabase.from('chat_sessions').select('*').eq('id',id).single();if(error){console.error('Error fetching chat session:',error);return null;}return{...data,messages:data.messages||[]};}catch(error){console.error('Error in getChatSessionById:',error);return null;}};export const updateChatSession=async(id,updates)=>{try{const{data,error}=await supabase.from('chat_sessions').update(updates).eq('id',id).select().single();if(error){console.error('Error updating chat session:',error);return null;}return{...data,messages:data.messages||[]};}catch(error){console.error('Error in updateChatSession:',error);return null;}};export const updateChatTitle=async(id,title)=>{try{const{error}=await supabase.from('chat_sessions').update({title}).eq('id',id);if(error){console.error('Error updating chat title:',error);return false;}return true;}catch(error){console.error('Error in updateChatTitle:',error);return false;}};export const deleteChatSession=async id=>{try{const{error}=await supabase.from('chat_sessions').delete().eq('id',id);if(error){console.error('Error deleting chat session:',error);return false;}return true;}catch(error){console.error('Error in deleteChatSession:',error);return false;}};export const saveChatMessages=async(chatId,messages)=>{try{// Ensure all message fields are preserved when saving to Supabase\nconst sanitizedMessages=messages.map(msg=>({id:msg.id,content:msg.content,document_answer:msg.document_answer||undefined,website_answer:msg.website_answer||undefined,llm_model:msg.llm_model,sender:msg.sender,loading:msg.loading,sources:msg.sources||undefined,document_sources:msg.document_sources||undefined,website_sources:msg.website_sources||undefined,timestamp:msg.timestamp,chatId:msg.chatId,llm_fallback_used:msg.llm_fallback_used}));// Check if chat has documents or websites in the messages\nconst hasDocument=sanitizedMessages.some(msg=>msg.document_answer||msg.document_sources&&msg.document_sources.length>0||msg.sources&&msg.sources.some(s=>s.source_type==='document'));const hasWebsite=sanitizedMessages.some(msg=>msg.website_answer||msg.website_sources&&msg.website_sources.length>0||msg.sources&&msg.sources.some(s=>s.source_type==='website'));console.log('Saving chat messages:',{messageCount:sanitizedMessages.length,hasDocument,hasWebsite,messagesWithDocAnswer:sanitizedMessages.filter(m=>m.document_answer).length,messagesWithWebAnswer:sanitizedMessages.filter(m=>m.website_answer).length});const{error}=await supabase.from('chat_sessions').update({messages:sanitizedMessages,has_document:hasDocument,has_website:hasWebsite}).eq('id',chatId);if(error){console.error('Error saving chat messages:',error);return false;}return true;}catch(error){console.error('Error in saveChatMessages:',error);return false;}};export const clearAllChatSessions=async userId=>{try{let query=supabase.from('chat_sessions').delete();if(userId){query=query.eq('user_id',userId);}const{error}=await query;if(error){console.error('Error clearing chat sessions:',error);return false;}return true;}catch(error){console.error('Error in clearAllChatSessions:',error);return false;}};export default supabase;", "map": {"version": 3, "names": ["supabaseClient", "checkSupabaseConnection", "supabase", "testSupabaseConnection", "getDocuments", "data", "error", "from", "select", "order", "ascending", "console", "getDocumentById", "id", "eq", "single", "getDocumentChunks", "documentId", "getWebsites", "getWebsiteById", "saveQuery", "query", "insert", "getRecentQueries", "limit", "arguments", "length", "undefined", "uploadFile", "filePath", "file", "onProgress", "log", "name", "size", "type", "storage", "upload", "cacheControl", "upsert", "contentType", "warn", "publicUrl", "getPublicUrl", "path", "strategy1Error", "simplePath", "Date", "now", "strategy2Error", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "Error", "statusText", "result", "json", "storage_url", "local_path", "strategy3Error", "strategy1", "strategy2", "strategy3", "getFileUrl", "createChatSession", "title", "modelUsed", "messages", "model_used", "has_document", "has_website", "getChatSessions", "userId", "tableCheck", "tableError", "message", "sessions", "map", "session", "created_at", "updated_at", "getChatSessionById", "updateChatSession", "updates", "update", "updateChatTitle", "deleteChatSession", "delete", "saveChatMessages", "chatId", "sanitizedMessages", "msg", "content", "document_answer", "website_answer", "llm_model", "sender", "loading", "sources", "document_sources", "website_sources", "timestamp", "llm_fallback_used", "hasDocument", "some", "s", "source_type", "hasWebsite", "messageCount", "messagesWithDocAnswer", "filter", "m", "messagesWithWebAnswer", "clearAllChatSessions"], "sources": ["C:/IR App/frontend/src/services/supabase.ts"], "sourcesContent": ["// Supabase client for frontend\nimport supabaseClient, { checkSupabaseConnection } from '../utils/supabaseClient';\n\n// Export the client for use throughout the application\nexport const supabase = supabaseClient;\n\n// Export the connection check function\nexport const testSupabaseConnection = checkSupabaseConnection;\n\n// Document types\nexport interface Document {\n  id: string;\n  filename: string;\n  display_name?: string;\n  file_path: string;\n  file_type?: string;\n  file_size?: number;\n  main_category?: string;\n  category?: string;\n  sub_category?: string;\n  minor_category?: string;\n  uploaded_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface DocumentChunk {\n  id: string;\n  document_id: string;\n  chunk_index: number;\n  page_number: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Website types\nexport interface Website {\n  id: string;\n  url: string;\n  domain?: string;\n  title?: string;\n  description?: string;\n  category?: string;\n  submitted_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface WebsiteChunk {\n  id: string;\n  website_id: string;\n  chunk_index: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Query types\nexport interface Query {\n  id: string;\n  user_id?: string;\n  query_text: string;\n  answer_text?: string;\n  llm_model?: string;\n  sources?: any;\n  created_at?: string;\n  processing_time?: number;\n}\n\n// Document operations\nexport const getDocuments = async (): Promise<Document[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching documents:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocuments:', error);\n    return [];\n  }\n};\n\nexport const getDocumentById = async (id: string): Promise<Document | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching document:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getDocumentById:', error);\n    return null;\n  }\n};\n\nexport const getDocumentChunks = async (documentId: string): Promise<DocumentChunk[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('document_chunks')\n      .select('*')\n      .eq('document_id', documentId)\n      .order('chunk_index', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching document chunks:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocumentChunks:', error);\n    return [];\n  }\n};\n\n// Website operations\nexport const getWebsites = async (): Promise<Website[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching websites:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getWebsites:', error);\n    return [];\n  }\n};\n\nexport const getWebsiteById = async (id: string): Promise<Website | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching website:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getWebsiteById:', error);\n    return null;\n  }\n};\n\n// Query operations\nexport const saveQuery = async (query: Omit<Query, 'id' | 'created_at'>): Promise<Query | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .insert([query])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error saving query:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in saveQuery:', error);\n    return null;\n  }\n};\n\nexport const getRecentQueries = async (limit: number = 10): Promise<Query[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit);\n\n    if (error) {\n      console.error('Error fetching recent queries:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getRecentQueries:', error);\n    return [];\n  }\n};\n\n// Storage operations\nexport const uploadFile = async (\n  filePath: string,\n  file: File,\n  onProgress?: (progress: number) => void\n): Promise<string | null> => {\n  try {\n    console.log(`🔄 Starting file upload: ${filePath}`);\n    console.log(`📁 File details: ${file.name}, ${file.size} bytes, ${file.type}`);\n    \n    // Try multiple upload strategies to handle RLS issues\n    \n    // Strategy 1: Direct upload with auth context\n    try {\n      const { data, error } = await supabase.storage\n        .from('documents')\n        .upload(filePath, file, {\n          cacheControl: '3600',\n          upsert: true,  // Allow overwriting\n          contentType: file.type || 'application/octet-stream'\n        });\n\n      if (error) {\n        console.warn(`⚠️ Strategy 1 failed:`, error);\n        throw error;\n      }\n\n      // Get public URL\n      const { data: { publicUrl } } = supabase.storage\n        .from('documents')\n        .getPublicUrl(data.path);\n\n      console.log(`✅ Strategy 1 success: ${publicUrl}`);\n      return publicUrl;\n      \n    } catch (strategy1Error) {\n      console.warn(`Strategy 1 failed, trying Strategy 2:`, strategy1Error);\n      \n      // Strategy 2: Upload with simplified path and anonymous context\n      try {\n        // Create simpler file path\n        const simplePath = `${Date.now()}_${file.name}`;\n        \n        const { data, error } = await supabase.storage\n          .from('documents')\n          .upload(simplePath, file, {\n            upsert: true,\n            contentType: file.type\n          });\n\n        if (error) {\n          console.warn(`⚠️ Strategy 2 failed:`, error);\n          throw error;\n        }\n\n        const { data: { publicUrl } } = supabase.storage\n          .from('documents')\n          .getPublicUrl(data.path);\n\n        console.log(`✅ Strategy 2 success: ${publicUrl}`);\n        return publicUrl;\n        \n      } catch (strategy2Error) {\n        console.warn(`Strategy 2 failed, trying Strategy 3:`, strategy2Error);\n        \n        // Strategy 3: Use backend upload endpoint as fallback\n        try {\n          const formData = new FormData();\n          formData.append('file', file);\n          formData.append('uploaded_by', 'frontend_user');\n          \n          const response = await fetch('/api/upload-document', {\n            method: 'POST',\n            body: formData\n          });\n          \n          if (!response.ok) {\n            throw new Error(`Backend upload failed: ${response.statusText}`);\n          }\n          \n          const result = await response.json();\n          console.log(`✅ Strategy 3 success via backend:`, result);\n          \n          return result.storage_url || result.local_path || 'uploaded_via_backend';\n          \n        } catch (strategy3Error) {\n          console.error(`❌ All upload strategies failed:`, {\n            strategy1: strategy1Error,\n            strategy2: strategy2Error,\n            strategy3: strategy3Error\n          });\n          \n          // Return null to indicate failure\n          return null;\n        }\n      }\n    }\n    \n  } catch (error) {\n    console.error('❌ Critical upload error:', error);\n    return null;\n  }\n};\n\nexport const getFileUrl = (filePath: string): string => {\n  const { data: { publicUrl } } = supabase.storage\n    .from('documents')\n    .getPublicUrl(filePath);\n\n  return publicUrl;\n};\n\n// Chat Session types\nexport interface ChatMessage {\n  id: string;\n  content: string;\n  document_answer?: string;\n  website_answer?: string;\n  llm_model?: string;\n  sender: 'user' | 'ai';\n  loading?: boolean;\n  sources?: Array<any>;\n  document_sources?: Array<any>;\n  website_sources?: Array<any>;\n  timestamp?: string;\n  chatId?: string;\n  llm_fallback_used?: boolean;  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n}\n\nexport interface ChatSession {\n  id: string;\n  user_id?: string;\n  title: string;\n  messages: ChatMessage[];\n  model_used: string;\n  created_at: string;\n  updated_at: string;\n  tags?: string[];\n  has_document: boolean;\n  has_website: boolean;\n}\n\n// Chat Session operations\nexport const createChatSession = async (title: string = 'New Chat', modelUsed: string = 'gemini-2.0-flash'): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .insert([{\n        title,\n        messages: [],\n        model_used: modelUsed,\n        has_document: false,\n        has_website: false\n      }])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in createChatSession:', error);\n    return null;\n  }\n};\n\nexport const getChatSessions = async (userId?: string): Promise<ChatSession[]> => {\n  try {\n    console.log('Attempting to fetch chat sessions...');\n\n    // First, try to check if the table exists with a simple query\n    const { data: tableCheck, error: tableError } = await supabase\n      .from('chat_sessions')\n      .select('id')\n      .limit(1);\n\n    if (tableError) {\n      console.warn('Chat sessions table not accessible:', tableError.message);\n      console.log('Returning empty chat sessions array');\n      return [];\n    }\n\n    // If table exists, try the full query with basic columns only\n    let query = supabase\n      .from('chat_sessions')\n      .select('id, title, created_at, updated_at')\n      .order('updated_at', { ascending: false })\n      .limit(10); // Reduced limit\n\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { data, error } = await query;\n\n    if (error) {\n      console.warn('Chat sessions query failed:', error.message);\n      return [];\n    }\n\n    // Process and return data with safe defaults\n    const sessions = (data || []).map(session => ({\n      id: session.id,\n      title: session.title || 'Untitled Chat',\n      created_at: session.created_at,\n      updated_at: session.updated_at,\n      model_used: 'gemini-2.0-flash',\n      has_document: false,\n      has_website: false,\n      messages: []\n    }));\n\n    console.log(`Successfully loaded ${sessions.length} chat sessions`);\n    return sessions;\n\n  } catch (error: any) {\n    console.warn('Error in getChatSessions:', error.message || error);\n    return [];\n  }\n};\n\nexport const getChatSessionById = async (id: string): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in getChatSessionById:', error);\n    return null;\n  }\n};\n\nexport const updateChatSession = async (id: string, updates: Partial<ChatSession>): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error updating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in updateChatSession:', error);\n    return null;\n  }\n};\n\nexport const updateChatTitle = async (id: string, title: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ title })\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error updating chat title:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in updateChatTitle:', error);\n    return false;\n  }\n};\n\nexport const deleteChatSession = async (id: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error deleting chat session:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in deleteChatSession:', error);\n    return false;\n  }\n};\n\nexport const saveChatMessages = async (chatId: string, messages: ChatMessage[]): Promise<boolean> => {\n  try {\n    // Ensure all message fields are preserved when saving to Supabase\n    const sanitizedMessages = messages.map(msg => ({\n      id: msg.id,\n      content: msg.content,\n      document_answer: msg.document_answer || undefined,\n      website_answer: msg.website_answer || undefined,\n      llm_model: msg.llm_model,\n      sender: msg.sender,\n      loading: msg.loading,\n      sources: msg.sources || undefined,\n      document_sources: msg.document_sources || undefined,\n      website_sources: msg.website_sources || undefined,\n      timestamp: msg.timestamp,\n      chatId: msg.chatId,\n      llm_fallback_used: msg.llm_fallback_used\n    }));\n\n    // Check if chat has documents or websites in the messages\n    const hasDocument = sanitizedMessages.some(msg => \n      msg.document_answer || \n      (msg.document_sources && msg.document_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'document'))\n    );\n    \n    const hasWebsite = sanitizedMessages.some(msg => \n      msg.website_answer || \n      (msg.website_sources && msg.website_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'website'))\n    );\n\n    console.log('Saving chat messages:', {\n      messageCount: sanitizedMessages.length,\n      hasDocument,\n      hasWebsite,\n      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,\n      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length\n    });\n\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ \n        messages: sanitizedMessages,\n        has_document: hasDocument,\n        has_website: hasWebsite\n      })\n      .eq('id', chatId);\n\n    if (error) {\n      console.error('Error saving chat messages:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in saveChatMessages:', error);\n    return false;\n  }\n};\n\nexport const clearAllChatSessions = async (userId?: string): Promise<boolean> => {\n  try {\n    let query = supabase.from('chat_sessions').delete();\n    \n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { error } = await query;\n\n    if (error) {\n      console.error('Error clearing chat sessions:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in clearAllChatSessions:', error);\n    return false;\n  }\n};\n\nexport default supabase;\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,cAAc,EAAIC,uBAAuB,KAAQ,yBAAyB,CAEjF;AACA,MAAO,MAAM,CAAAC,QAAQ,CAAGF,cAAc,CAEtC;AACA,MAAO,MAAM,CAAAG,sBAAsB,CAAGF,uBAAuB,CAE7D;AA6BA;AAwBA;AAYA;AACA,MAAO,MAAM,CAAAG,YAAY,CAAG,KAAAA,CAAA,GAAiC,CAC3D,GAAI,CACF,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE5C,GAAIJ,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,EAAE,CACX,CAEA,MAAO,CAAAD,IAAI,EAAI,EAAE,CACnB,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,EAAE,CACX,CACF,CAAC,CAED,MAAO,MAAM,CAAAM,eAAe,CAAG,KAAO,CAAAC,EAAU,EAA+B,CAC7E,GAAI,CACF,KAAM,CAAER,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,CAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC,CAEX,GAAIT,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,KAAI,CACb,CAEA,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAU,iBAAiB,CAAG,KAAO,CAAAC,UAAkB,EAA+B,CACvF,GAAI,CACF,KAAM,CAAEZ,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,iBAAiB,CAAC,CACvBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,aAAa,CAAEG,UAAU,CAAC,CAC7BR,KAAK,CAAC,aAAa,CAAE,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAE5C,GAAIJ,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,MAAO,EAAE,CACX,CAEA,MAAO,CAAAD,IAAI,EAAI,EAAE,CACnB,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAY,WAAW,CAAG,KAAAA,CAAA,GAAgC,CACzD,GAAI,CACF,KAAM,CAAEb,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE5C,GAAIJ,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,EAAE,CACX,CAEA,MAAO,CAAAD,IAAI,EAAI,EAAE,CACnB,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,EAAE,CACX,CACF,CAAC,CAED,MAAO,MAAM,CAAAa,cAAc,CAAG,KAAO,CAAAN,EAAU,EAA8B,CAC3E,GAAI,CACF,KAAM,CAAER,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,CAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC,CAEX,GAAIT,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,KAAI,CACb,CAEA,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAc,SAAS,CAAG,KAAO,CAAAC,KAAuC,EAA4B,CACjG,GAAI,CACF,KAAM,CAAEhB,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,SAAS,CAAC,CACfe,MAAM,CAAC,CAACD,KAAK,CAAC,CAAC,CACfb,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC,CAEX,GAAIT,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,MAAO,KAAI,CACb,CAEA,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAiB,gBAAgB,CAAG,cAAAA,CAAA,CAAgD,IAAzC,CAAAC,KAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACvD,GAAI,CACF,KAAM,CAAEpB,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACzCc,KAAK,CAACA,KAAK,CAAC,CAEf,GAAIlB,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,EAAE,CACX,CAEA,MAAO,CAAAD,IAAI,EAAI,EAAE,CACnB,CAAE,MAAOC,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAsB,UAAU,CAAG,KAAAA,CACxBC,QAAgB,CAChBC,IAAU,CACVC,UAAuC,GACZ,CAC3B,GAAI,CACFpB,OAAO,CAACqB,GAAG,CAAC,4BAA4BH,QAAQ,EAAE,CAAC,CACnDlB,OAAO,CAACqB,GAAG,CAAC,oBAAoBF,IAAI,CAACG,IAAI,KAAKH,IAAI,CAACI,IAAI,WAAWJ,IAAI,CAACK,IAAI,EAAE,CAAC,CAE9E;AAEA;AACA,GAAI,CACF,KAAM,CAAE9B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAACkC,OAAO,CAC3C7B,IAAI,CAAC,WAAW,CAAC,CACjB8B,MAAM,CAACR,QAAQ,CAAEC,IAAI,CAAE,CACtBQ,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,IAAI,CAAG;AACfC,WAAW,CAAEV,IAAI,CAACK,IAAI,EAAI,0BAC5B,CAAC,CAAC,CAEJ,GAAI7B,KAAK,CAAE,CACTK,OAAO,CAAC8B,IAAI,CAAC,uBAAuB,CAAEnC,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CAEA;AACA,KAAM,CAAED,IAAI,CAAE,CAAEqC,SAAU,CAAE,CAAC,CAAGxC,QAAQ,CAACkC,OAAO,CAC7C7B,IAAI,CAAC,WAAW,CAAC,CACjBoC,YAAY,CAACtC,IAAI,CAACuC,IAAI,CAAC,CAE1BjC,OAAO,CAACqB,GAAG,CAAC,yBAAyBU,SAAS,EAAE,CAAC,CACjD,MAAO,CAAAA,SAAS,CAElB,CAAE,MAAOG,cAAc,CAAE,CACvBlC,OAAO,CAAC8B,IAAI,CAAC,uCAAuC,CAAEI,cAAc,CAAC,CAErE;AACA,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIlB,IAAI,CAACG,IAAI,EAAE,CAE/C,KAAM,CAAE5B,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAACkC,OAAO,CAC3C7B,IAAI,CAAC,WAAW,CAAC,CACjB8B,MAAM,CAACS,UAAU,CAAEhB,IAAI,CAAE,CACxBS,MAAM,CAAE,IAAI,CACZC,WAAW,CAAEV,IAAI,CAACK,IACpB,CAAC,CAAC,CAEJ,GAAI7B,KAAK,CAAE,CACTK,OAAO,CAAC8B,IAAI,CAAC,uBAAuB,CAAEnC,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CAEA,KAAM,CAAED,IAAI,CAAE,CAAEqC,SAAU,CAAE,CAAC,CAAGxC,QAAQ,CAACkC,OAAO,CAC7C7B,IAAI,CAAC,WAAW,CAAC,CACjBoC,YAAY,CAACtC,IAAI,CAACuC,IAAI,CAAC,CAE1BjC,OAAO,CAACqB,GAAG,CAAC,yBAAyBU,SAAS,EAAE,CAAC,CACjD,MAAO,CAAAA,SAAS,CAElB,CAAE,MAAOO,cAAc,CAAE,CACvBtC,OAAO,CAAC8B,IAAI,CAAC,uCAAuC,CAAEQ,cAAc,CAAC,CAErE;AACA,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEtB,IAAI,CAAC,CAC7BoB,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAE,eAAe,CAAC,CAE/C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,sBAAsB,CAAE,CACnDC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEN,QACR,CAAC,CAAC,CAEF,GAAI,CAACG,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,CAAC,0BAA0BL,QAAQ,CAACM,UAAU,EAAE,CAAC,CAClE,CAEA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAP,QAAQ,CAACQ,IAAI,CAAC,CAAC,CACpClD,OAAO,CAACqB,GAAG,CAAC,mCAAmC,CAAE4B,MAAM,CAAC,CAExD,MAAO,CAAAA,MAAM,CAACE,WAAW,EAAIF,MAAM,CAACG,UAAU,EAAI,sBAAsB,CAE1E,CAAE,MAAOC,cAAc,CAAE,CACvBrD,OAAO,CAACL,KAAK,CAAC,iCAAiC,CAAE,CAC/C2D,SAAS,CAAEpB,cAAc,CACzBqB,SAAS,CAAEjB,cAAc,CACzBkB,SAAS,CAAEH,cACb,CAAC,CAAC,CAEF;AACA,MAAO,KAAI,CACb,CACF,CACF,CAEF,CAAE,MAAO1D,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAA8D,UAAU,CAAIvC,QAAgB,EAAa,CACtD,KAAM,CAAExB,IAAI,CAAE,CAAEqC,SAAU,CAAE,CAAC,CAAGxC,QAAQ,CAACkC,OAAO,CAC7C7B,IAAI,CAAC,WAAW,CAAC,CACjBoC,YAAY,CAACd,QAAQ,CAAC,CAEzB,MAAO,CAAAa,SAAS,CAClB,CAAC,CAED;AA8BA;AACA,MAAO,MAAM,CAAA2B,iBAAiB,CAAG,cAAAA,CAAA,CAA2G,IAApG,CAAAC,KAAa,CAAA7C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,UAAU,IAAE,CAAA8C,SAAiB,CAAA9C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,kBAAkB,CACxG,GAAI,CACF,KAAM,CAAEpB,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,eAAe,CAAC,CACrBe,MAAM,CAAC,CAAC,CACPgD,KAAK,CACLE,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAEF,SAAS,CACrBG,YAAY,CAAE,KAAK,CACnBC,WAAW,CAAE,KACf,CAAC,CAAC,CAAC,CACFnE,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC,CAEX,GAAIT,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,KAAI,CACb,CAEA,MAAO,CACL,GAAGD,IAAI,CACPmE,QAAQ,CAAEnE,IAAI,CAACmE,QAAQ,EAAI,EAC7B,CAAC,CACH,CAAE,MAAOlE,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAsE,eAAe,CAAG,KAAO,CAAAC,MAAe,EAA6B,CAChF,GAAI,CACFlE,OAAO,CAACqB,GAAG,CAAC,sCAAsC,CAAC,CAEnD;AACA,KAAM,CAAE3B,IAAI,CAAEyE,UAAU,CAAExE,KAAK,CAAEyE,UAAW,CAAC,CAAG,KAAM,CAAA7E,QAAQ,CAC3DK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,IAAI,CAAC,CACZgB,KAAK,CAAC,CAAC,CAAC,CAEX,GAAIuD,UAAU,CAAE,CACdpE,OAAO,CAAC8B,IAAI,CAAC,qCAAqC,CAAEsC,UAAU,CAACC,OAAO,CAAC,CACvErE,OAAO,CAACqB,GAAG,CAAC,qCAAqC,CAAC,CAClD,MAAO,EAAE,CACX,CAEA;AACA,GAAI,CAAAX,KAAK,CAAGnB,QAAQ,CACjBK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,mCAAmC,CAAC,CAC3CC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACzCc,KAAK,CAAC,EAAE,CAAC,CAAE;AAEd,GAAIqD,MAAM,CAAE,CACVxD,KAAK,CAAGA,KAAK,CAACP,EAAE,CAAC,SAAS,CAAE+D,MAAM,CAAC,CACrC,CAEA,KAAM,CAAExE,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAe,KAAK,CAEnC,GAAIf,KAAK,CAAE,CACTK,OAAO,CAAC8B,IAAI,CAAC,6BAA6B,CAAEnC,KAAK,CAAC0E,OAAO,CAAC,CAC1D,MAAO,EAAE,CACX,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,CAAC5E,IAAI,EAAI,EAAE,EAAE6E,GAAG,CAACC,OAAO,GAAK,CAC5CtE,EAAE,CAAEsE,OAAO,CAACtE,EAAE,CACdyD,KAAK,CAAEa,OAAO,CAACb,KAAK,EAAI,eAAe,CACvCc,UAAU,CAAED,OAAO,CAACC,UAAU,CAC9BC,UAAU,CAAEF,OAAO,CAACE,UAAU,CAC9BZ,UAAU,CAAE,kBAAkB,CAC9BC,YAAY,CAAE,KAAK,CACnBC,WAAW,CAAE,KAAK,CAClBH,QAAQ,CAAE,EACZ,CAAC,CAAC,CAAC,CAEH7D,OAAO,CAACqB,GAAG,CAAC,uBAAuBiD,QAAQ,CAACvD,MAAM,gBAAgB,CAAC,CACnE,MAAO,CAAAuD,QAAQ,CAEjB,CAAE,MAAO3E,KAAU,CAAE,CACnBK,OAAO,CAAC8B,IAAI,CAAC,2BAA2B,CAAEnC,KAAK,CAAC0E,OAAO,EAAI1E,KAAK,CAAC,CACjE,MAAO,EAAE,CACX,CACF,CAAC,CAED,MAAO,MAAM,CAAAgF,kBAAkB,CAAG,KAAO,CAAAzE,EAAU,EAAkC,CACnF,GAAI,CACF,KAAM,CAAER,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,CAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC,CAEX,GAAIT,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,KAAI,CACb,CAEA,MAAO,CACL,GAAGD,IAAI,CACPmE,QAAQ,CAAEnE,IAAI,CAACmE,QAAQ,EAAI,EAC7B,CAAC,CACH,CAAE,MAAOlE,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAiF,iBAAiB,CAAG,KAAAA,CAAO1E,EAAU,CAAE2E,OAA6B,GAAkC,CACjH,GAAI,CACF,KAAM,CAAEnF,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CACnCK,IAAI,CAAC,eAAe,CAAC,CACrBkF,MAAM,CAACD,OAAO,CAAC,CACf1E,EAAE,CAAC,IAAI,CAAED,EAAE,CAAC,CACZL,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC,CAEX,GAAIT,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,KAAI,CACb,CAEA,MAAO,CACL,GAAGD,IAAI,CACPmE,QAAQ,CAAEnE,IAAI,CAACmE,QAAQ,EAAI,EAC7B,CAAC,CACH,CAAE,MAAOlE,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAoF,eAAe,CAAG,KAAAA,CAAO7E,EAAU,CAAEyD,KAAa,GAAuB,CACpF,GAAI,CACF,KAAM,CAAEhE,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAC7BK,IAAI,CAAC,eAAe,CAAC,CACrBkF,MAAM,CAAC,CAAEnB,KAAM,CAAC,CAAC,CACjBxD,EAAE,CAAC,IAAI,CAAED,EAAE,CAAC,CAEf,GAAIP,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAE,MAAOA,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,MAAK,CACd,CACF,CAAC,CAED,MAAO,MAAM,CAAAqF,iBAAiB,CAAG,KAAO,CAAA9E,EAAU,EAAuB,CACvE,GAAI,CACF,KAAM,CAAEP,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAC7BK,IAAI,CAAC,eAAe,CAAC,CACrBqF,MAAM,CAAC,CAAC,CACR9E,EAAE,CAAC,IAAI,CAAED,EAAE,CAAC,CAEf,GAAIP,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAE,MAAOA,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,MAAK,CACd,CACF,CAAC,CAED,MAAO,MAAM,CAAAuF,gBAAgB,CAAG,KAAAA,CAAOC,MAAc,CAAEtB,QAAuB,GAAuB,CACnG,GAAI,CACF;AACA,KAAM,CAAAuB,iBAAiB,CAAGvB,QAAQ,CAACU,GAAG,CAACc,GAAG,GAAK,CAC7CnF,EAAE,CAAEmF,GAAG,CAACnF,EAAE,CACVoF,OAAO,CAAED,GAAG,CAACC,OAAO,CACpBC,eAAe,CAAEF,GAAG,CAACE,eAAe,EAAIvE,SAAS,CACjDwE,cAAc,CAAEH,GAAG,CAACG,cAAc,EAAIxE,SAAS,CAC/CyE,SAAS,CAAEJ,GAAG,CAACI,SAAS,CACxBC,MAAM,CAAEL,GAAG,CAACK,MAAM,CAClBC,OAAO,CAAEN,GAAG,CAACM,OAAO,CACpBC,OAAO,CAAEP,GAAG,CAACO,OAAO,EAAI5E,SAAS,CACjC6E,gBAAgB,CAAER,GAAG,CAACQ,gBAAgB,EAAI7E,SAAS,CACnD8E,eAAe,CAAET,GAAG,CAACS,eAAe,EAAI9E,SAAS,CACjD+E,SAAS,CAAEV,GAAG,CAACU,SAAS,CACxBZ,MAAM,CAAEE,GAAG,CAACF,MAAM,CAClBa,iBAAiB,CAAEX,GAAG,CAACW,iBACzB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAC,WAAW,CAAGb,iBAAiB,CAACc,IAAI,CAACb,GAAG,EAC5CA,GAAG,CAACE,eAAe,EAClBF,GAAG,CAACQ,gBAAgB,EAAIR,GAAG,CAACQ,gBAAgB,CAAC9E,MAAM,CAAG,CAAE,EACxDsE,GAAG,CAACO,OAAO,EAAIP,GAAG,CAACO,OAAO,CAACM,IAAI,CAAEC,CAAM,EAAKA,CAAC,CAACC,WAAW,GAAK,UAAU,CAC3E,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGjB,iBAAiB,CAACc,IAAI,CAACb,GAAG,EAC3CA,GAAG,CAACG,cAAc,EACjBH,GAAG,CAACS,eAAe,EAAIT,GAAG,CAACS,eAAe,CAAC/E,MAAM,CAAG,CAAE,EACtDsE,GAAG,CAACO,OAAO,EAAIP,GAAG,CAACO,OAAO,CAACM,IAAI,CAAEC,CAAM,EAAKA,CAAC,CAACC,WAAW,GAAK,SAAS,CAC1E,CAAC,CAEDpG,OAAO,CAACqB,GAAG,CAAC,uBAAuB,CAAE,CACnCiF,YAAY,CAAElB,iBAAiB,CAACrE,MAAM,CACtCkF,WAAW,CACXI,UAAU,CACVE,qBAAqB,CAAEnB,iBAAiB,CAACoB,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAClB,eAAe,CAAC,CAACxE,MAAM,CAC9E2F,qBAAqB,CAAEtB,iBAAiB,CAACoB,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACjB,cAAc,CAAC,CAACzE,MACzE,CAAC,CAAC,CAEF,KAAM,CAAEpB,KAAM,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAC7BK,IAAI,CAAC,eAAe,CAAC,CACrBkF,MAAM,CAAC,CACNjB,QAAQ,CAAEuB,iBAAiB,CAC3BrB,YAAY,CAAEkC,WAAW,CACzBjC,WAAW,CAAEqC,UACf,CAAC,CAAC,CACDlG,EAAE,CAAC,IAAI,CAAEgF,MAAM,CAAC,CAEnB,GAAIxF,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAE,MAAOA,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,MAAK,CACd,CACF,CAAC,CAED,MAAO,MAAM,CAAAgH,oBAAoB,CAAG,KAAO,CAAAzC,MAAe,EAAuB,CAC/E,GAAI,CACF,GAAI,CAAAxD,KAAK,CAAGnB,QAAQ,CAACK,IAAI,CAAC,eAAe,CAAC,CAACqF,MAAM,CAAC,CAAC,CAEnD,GAAIf,MAAM,CAAE,CACVxD,KAAK,CAAGA,KAAK,CAACP,EAAE,CAAC,SAAS,CAAE+D,MAAM,CAAC,CACrC,CAEA,KAAM,CAAEvE,KAAM,CAAC,CAAG,KAAM,CAAAe,KAAK,CAE7B,GAAIf,KAAK,CAAE,CACTK,OAAO,CAACL,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAE,MAAOA,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,MAAK,CACd,CACF,CAAC,CAED,cAAe,CAAAJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}