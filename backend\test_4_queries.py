#!/usr/bin/env python
"""
Test script for the 4 specific query types mentioned in requirements:
1. ACP full form (documents only)
2. Rapid response app (websites only) 
3. VASP had developed (both documents and websites)
4. WDG4G details (LLM fallback)
"""

import sys
import os
import json
import asyncio
from typing import Dict, Any

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import server modules
from server import QueryRequest, app
from supabase_client import supabase
import llm_router

# Test queries
TEST_QUERIES = [
    {
        "name": "ACP full form (documents only)",
        "query": "full form of ACP",
        "expected_source_type": "document",
        "description": "Should find document sources only with ACP full form definition"
    },
    {
        "name": "Rapid response app (websites only)",
        "query": "rapid response app for transportation safety",
        "expected_source_type": "website", 
        "description": "Should find website sources only about rapid response applications"
    },
    {
        "name": "VASP had developed (both documents and websites)",
        "query": "what has VASP developed",
        "expected_source_type": "both",
        "description": "Should find both document and website sources about VASP developments"
    },
    {
        "name": "WDG4G details (LLM fallback)",
        "query": "WDG4G technical specifications and details",
        "expected_source_type": "llm_fallback",
        "description": "Should trigger LLM fallback when no relevant sources found"
    }
]

async def test_query_endpoint(query_request: QueryRequest) -> Dict[str, Any]:
    """Test the query endpoint with a given request."""
    try:
        # Import the query processing function from server
        from server import generate_llm_answer, search_supabase_document_chunks_enhanced, search_supabase_website_chunks, generate_embedding
        
        print(f"\n🔍 Testing query: '{query_request.query}'")
        
        # Generate embedding for the query
        query_embedding = generate_embedding(query_request.query, query_request.model)
        if not query_embedding:
            return {"error": "Failed to generate embedding"}
        
        # Search documents with ultra-low threshold
        doc_chunks = search_supabase_document_chunks_enhanced(
            query_embedding=query_embedding,
            query_text=query_request.query,
            use_hybrid_search=query_request.use_hybrid_search,
            top_k=30,
            min_threshold=0.01
        )
        
        # Search websites with ultra-low threshold
        website_chunks = search_supabase_website_chunks(
            query_embedding=query_embedding,
            query_text=query_request.query,
            use_hybrid_search=query_request.use_hybrid_search,
            top_k=30,
            min_threshold=0.01
        )
        
        print(f"📄 Found {len(doc_chunks)} document chunks")
        print(f"🌐 Found {len(website_chunks)} website chunks")
        
        # Apply strict priority logic
        all_chunks = []
        
        # Priority 1: Documents (highest priority)
        if doc_chunks:
            all_chunks.extend(doc_chunks)
            source_priority = "documents"
        
        # Priority 2: Websites (medium priority) 
        if website_chunks:
            all_chunks.extend(website_chunks)
            if not doc_chunks:
                source_priority = "websites"
            else:
                source_priority = "documents+websites"
        
        # Priority 3: LLM fallback (lowest priority)
        if not all_chunks and query_request.fallback_enabled:
            print("🤖 No relevant sources found, using LLM fallback")
            
            # Generate LLM answer without context
            answer_text, _, _, _ = generate_llm_answer(
                query=query_request.query,
                similar_chunks=[],
                model_id=query_request.model,
                extract_format=query_request.extract_format
            )
            
            return {
                "answer": answer_text,
                "sources": [],
                "document_sources": [],
                "website_sources": [],
                "llm_fallback_used": True,
                "llm_model": query_request.model,
                "source_priority": "llm_fallback"
            }
        
        if not all_chunks:
            return {"error": "No relevant information found and LLM fallback disabled"}
        
        # Generate answer using found chunks
        answer_text, sources, document_sources, website_sources = generate_llm_answer(
            query=query_request.query,
            similar_chunks=all_chunks,
            model_id=query_request.model,
            extract_format=query_request.extract_format
        )
        
        return {
            "answer": answer_text,
            "sources": sources,
            "document_sources": document_sources,
            "website_sources": website_sources,
            "llm_fallback_used": False,
            "llm_model": query_request.model,
            "source_priority": source_priority
        }
        
    except Exception as e:
        print(f"❌ Error processing query: {str(e)}")
        return {"error": str(e)}

async def run_tests():
    """Run all test queries and report results."""
    print("🚀 Starting 4-Query Type Priority Test")
    print("=" * 60)
    
    results = []
    
    for i, test_case in enumerate(TEST_QUERIES, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"📝 Query: {test_case['query']}")
        print(f"🎯 Expected: {test_case['expected_source_type']}")
        print(f"📄 Description: {test_case['description']}")
        
        # Create query request
        query_request = QueryRequest(
            query=test_case['query'],
            model="gemini-2.0-flash",
            fallback_enabled=True,
            use_hybrid_search=True,
            retry_on_timeout=True
        )
        
        # Run the test
        result = await test_query_endpoint(query_request)
        
        # Analyze results
        if "error" in result:
            print(f"❌ FAILED: {result['error']}")
            status = "FAILED"
        else:
            # Check source types
            doc_count = len(result.get('document_sources', []))
            web_count = len(result.get('website_sources', []))
            llm_fallback = result.get('llm_fallback_used', False)
            
            print(f"📊 Results:")
            print(f"   📄 Document sources: {doc_count}")
            print(f"   🌐 Website sources: {web_count}")
            print(f"   🤖 LLM fallback used: {llm_fallback}")
            print(f"   🎯 Source priority: {result.get('source_priority', 'unknown')}")
            
            # Validate against expectations
            expected = test_case['expected_source_type']
            if expected == "document" and doc_count > 0 and web_count == 0:
                status = "✅ PASSED"
            elif expected == "website" and web_count > 0 and doc_count == 0:
                status = "✅ PASSED"
            elif expected == "both" and doc_count > 0 and web_count > 0:
                status = "✅ PASSED"
            elif expected == "llm_fallback" and llm_fallback:
                status = "✅ PASSED"
            else:
                status = "⚠️ UNEXPECTED"
            
            # Show answer preview
            answer = result.get('answer', '')
            if answer:
                preview = answer[:200] + "..." if len(answer) > 200 else answer
                print(f"💬 Answer preview: {preview}")
        
        print(f"🏁 Status: {status}")
        
        results.append({
            "test": test_case['name'],
            "query": test_case['query'],
            "expected": test_case['expected_source_type'],
            "status": status,
            "result": result
        })
        
        print("-" * 60)
    
    # Summary
    print(f"\n📈 SUMMARY")
    print("=" * 60)
    passed = sum(1 for r in results if "PASSED" in r['status'])
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    for result in results:
        print(f"{result['status']} {result['test']}")
    
    return results

if __name__ == "__main__":
    print("🔧 Initializing test environment...")
    
    # Test basic connectivity
    try:
        # Test Supabase connection
        test_query = "SELECT 1 as test"
        supabase_result = supabase.execute_query(test_query)
        print(f"✅ Supabase connected: {supabase_result}")
        
        # Test LLM router
        available_models = llm_router.get_available_models()
        print(f"✅ LLM Router available models: {[m['id'] for m in available_models]}")
        
        # Run the tests
        asyncio.run(run_tests())
        
    except Exception as e:
        print(f"❌ Test initialization failed: {str(e)}")
        import traceback
        traceback.print_exc()
