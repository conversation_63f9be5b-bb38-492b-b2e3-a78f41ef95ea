#!/usr/bin/env python3
"""
Quick test to verify the threshold fix is working.
"""

import requests
import json

def test_threshold_fix():
    """Test if the new 0.4 threshold is working properly."""
    print("🔧 Testing Threshold Fix")
    print("=" * 40)
    
    # Check server health
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server not healthy")
            return False
        print("✅ Server is healthy")
    except:
        print("❌ Server not responding")
        return False
    
    # Test WDG4G query (should trigger LLM fallback with proper threshold)
    print("\n🔍 Testing WDG4G query (should trigger LLM fallback)...")
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"query": "WDG4G technical specifications"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            doc_sources = len(result.get('document_sources', []))
            web_sources = len(result.get('website_sources', []))
            llm_fallback = result.get('llm_fallback_used', False)
            
            print(f"   📄 Document sources: {doc_sources}")
            print(f"   🌐 Website sources: {web_sources}")
            print(f"   🤖 LLM fallback used: {llm_fallback}")
            
            if llm_fallback:
                print("   ✅ SUCCESS: LLM fallback triggered (proper threshold working)")
                return True
            else:
                print("   ⚠️ ISSUE: LLM fallback not triggered (threshold may still be too low)")
                return False
        else:
            print(f"   ❌ Query failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False

def test_acp_query():
    """Test ACP query to see if it finds relevant documents."""
    print("\n🔍 Testing ACP query (should find documents)...")
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"query": "What is the full form of ACP?"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            doc_sources = len(result.get('document_sources', []))
            web_sources = len(result.get('website_sources', []))
            llm_fallback = result.get('llm_fallback_used', False)
            
            print(f"   📄 Document sources: {doc_sources}")
            print(f"   🌐 Website sources: {web_sources}")
            print(f"   🤖 LLM fallback used: {llm_fallback}")
            
            if doc_sources > 0 and not llm_fallback:
                print("   ✅ SUCCESS: Found document sources")
                return True
            elif llm_fallback:
                print("   ⚠️ ISSUE: LLM fallback used when documents should be found")
                return False
            else:
                print("   ⚠️ ISSUE: No sources found")
                return False
        else:
            print(f"   ❌ Query failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False

def main():
    """Main test function."""
    wdg4g_success = test_threshold_fix()
    acp_success = test_acp_query()
    
    print("\n" + "=" * 40)
    print("📊 SUMMARY")
    print(f"   WDG4G test (LLM fallback): {'✅ PASS' if wdg4g_success else '❌ FAIL'}")
    print(f"   ACP test (document search): {'✅ PASS' if acp_success else '❌ FAIL'}")
    
    if wdg4g_success and acp_success:
        print("\n🎉 Threshold fix is working correctly!")
        return True
    else:
        print("\n⚠️ Threshold fix needs more work")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
