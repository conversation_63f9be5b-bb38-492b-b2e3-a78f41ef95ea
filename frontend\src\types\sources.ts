// Unified Source interface definition for the entire application
export interface Source {
  // Core identification
  id?: string; // Generic ID field
  source_type: 'document' | 'website' | 'image' | 'table' | 'text';

  // Document-specific fields
  document_id?: string; // Document ID for database references
  filename?: string;
  name?: string; // Alternative name field for display
  page?: number;
  pages?: number[]; // Support for multi-page sources
  page_numbers?: number[]; // Alternative field name for page numbers

  // Website-specific fields
  website_id?: string; // Website ID for database references
  url?: string;
  link?: string; // For document viewer links or external URLs
  title?: string; // Website title

  // Visual content fields
  content_type?: 'text' | 'table' | 'image' | 'chart_diagram';
  visual_content?: {
    ocr_text?: string;
    is_logo?: boolean;
    project_context?: string;
    table_data?: any[];
    image_description?: string;
    [key: string]: any; // Allow additional metadata
  };

  // Storage and display
  storage_url?: string; // URL for stored visual content
  display_type?: 'text' | 'html_table' | 'image' | 'base64_image';

  // Metadata
  relevance_score?: number;
  chunk_id?: string;
  timestamp?: string;
}

// Type for processed sources used in UI components
export interface ProcessedSource {
  text: string;
  link?: string;
  isDocument?: boolean;
  relevance_score?: number;
}

// Type for source attribution and statistics
export interface SourceStatistics {
  total_sources: number;
  document_sources: number;
  website_sources: number;
  visual_sources: number;
  average_relevance: number;
}
