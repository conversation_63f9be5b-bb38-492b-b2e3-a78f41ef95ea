#!/usr/bin/env python3
"""
Check if website chunks exist in the database and diagnose website search issues.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Load environment variables
load_dotenv(os.path.join(os.path.dirname(__file__), 'backend', '.env'))

try:
    from supabase_client import supabase
    print("✅ Supabase client imported successfully")
except Exception as e:
    print(f"❌ Error importing Supabase client: {str(e)}")
    sys.exit(1)

def check_website_chunks():
    """Check website chunks in the database."""
    print("🔍 Checking website chunks...")
    
    try:
        # Check total count
        count_query = "SELECT COUNT(*) as count FROM website_chunks"
        count_result = supabase.execute_query(count_query)
        
        if isinstance(count_result, list) and len(count_result) > 0:
            count = count_result[0].get('count', 0)
            print(f"   Total website chunks: {count}")
        else:
            print("   Could not get count")
            count = 0
        
        if count > 0:
            # Get sample records
            sample_query = "SELECT * FROM website_chunks LIMIT 5"
            sample_result = supabase.execute_query(sample_query)
            
            if isinstance(sample_result, list) and len(sample_result) > 0:
                print(f"   Sample website chunks ({len(sample_result)}):")
                for i, record in enumerate(sample_result):
                    print(f"     {i+1}. URL: {record.get('url', 'N/A')}")
                    print(f"        Text preview: {record.get('text', '')[:100]}...")
                    print(f"        Source type: {record.get('source_type', 'N/A')}")
                    print()
            else:
                print("   No sample records retrieved")
        else:
            print("   ❌ No website chunks found!")
            
        return count
        
    except Exception as e:
        print(f"   ❌ Error checking website chunks: {str(e)}")
        return 0

def check_websites_table():
    """Check websites table."""
    print("🔍 Checking websites table...")
    
    try:
        # Check total count
        count_query = "SELECT COUNT(*) as count FROM websites"
        count_result = supabase.execute_query(count_query)
        
        if isinstance(count_result, list) and len(count_result) > 0:
            count = count_result[0].get('count', 0)
            print(f"   Total websites: {count}")
        else:
            print("   Could not get count")
            count = 0
        
        if count > 0:
            # Get sample records
            sample_query = "SELECT * FROM websites LIMIT 5"
            sample_result = supabase.execute_query(sample_query)
            
            if isinstance(sample_result, list) and len(sample_result) > 0:
                print(f"   Sample websites ({len(sample_result)}):")
                for i, record in enumerate(sample_result):
                    print(f"     {i+1}. URL: {record.get('url', 'N/A')}")
                    print(f"        Name: {record.get('name', 'N/A')}")
                    print(f"        Status: {record.get('status', 'N/A')}")
                    print()
            else:
                print("   No sample records retrieved")
        else:
            print("   ❌ No websites found!")
            
        return count
        
    except Exception as e:
        print(f"   ❌ Error checking websites: {str(e)}")
        return 0

def test_website_search_function():
    """Test the website search RPC function."""
    print("🔍 Testing website search RPC function...")
    
    try:
        # Test with dummy parameters
        result = supabase.rpc('search_website_chunks', {
            'query_embedding': [0.1] * 768,  # Dummy embedding
            'similarity_threshold': 0.1,
            'match_count': 5
        }).execute()
        
        if hasattr(result, 'data'):
            if result.data:
                print(f"   ✅ Function works, returned {len(result.data)} results")
                return True
            else:
                print("   ⚠️ Function works but returned no data")
                return True
        else:
            print("   ❌ Function failed - no data attribute")
            return False
            
    except Exception as e:
        print(f"   ❌ Function error: {str(e)}")
        return False

def check_rapidresponse_content():
    """Check if rapidresponseapp.com content exists."""
    print("🔍 Checking for rapidresponseapp.com content...")
    
    try:
        # Check in website_chunks
        query = "SELECT * FROM website_chunks WHERE url LIKE '%rapidresponse%' OR text LIKE '%rapid response%' LIMIT 5"
        result = supabase.execute_query(query)
        
        if isinstance(result, list) and len(result) > 0:
            print(f"   ✅ Found {len(result)} chunks with rapid response content")
            for i, chunk in enumerate(result):
                print(f"     {i+1}. URL: {chunk.get('url', 'N/A')}")
                print(f"        Text: {chunk.get('text', '')[:100]}...")
        else:
            print("   ❌ No rapid response content found in website_chunks")
            
        # Check in websites table
        query2 = "SELECT * FROM websites WHERE url LIKE '%rapidresponse%' OR name LIKE '%rapid%' LIMIT 5"
        result2 = supabase.execute_query(query2)
        
        if isinstance(result2, list) and len(result2) > 0:
            print(f"   ✅ Found {len(result2)} websites with rapid response")
            for i, website in enumerate(result2):
                print(f"     {i+1}. URL: {website.get('url', 'N/A')}")
                print(f"        Name: {website.get('name', 'N/A')}")
        else:
            print("   ❌ No rapid response websites found in websites table")
            
    except Exception as e:
        print(f"   ❌ Error checking rapid response content: {str(e)}")

def main():
    """Main function to diagnose website issues."""
    print("🔍 Website Chunks Diagnostic")
    print("=" * 50)
    
    # Check websites table
    website_count = check_websites_table()
    
    # Check website chunks
    chunk_count = check_website_chunks()
    
    # Test search function
    search_works = test_website_search_function()
    
    # Check specific content
    check_rapidresponse_content()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print(f"   Websites in database: {website_count}")
    print(f"   Website chunks in database: {chunk_count}")
    print(f"   Search function working: {'Yes' if search_works else 'No'}")
    
    if chunk_count == 0:
        print("\n💡 RECOMMENDATIONS:")
        print("   1. Add websites using the /api/websites/add endpoint")
        print("   2. Ensure website extraction is working properly")
        print("   3. Check if rapidresponseapp.com has been added")
        print("   4. Verify website chunks are being created during extraction")
    
    return chunk_count > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
