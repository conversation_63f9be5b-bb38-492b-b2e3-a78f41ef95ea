#!/usr/bin/env python3
"""
Check the content of the Supabase database to understand what data is available.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Load environment variables
load_dotenv(os.path.join(os.path.dirname(__file__), 'backend', '.env'))

try:
    from supabase_client import supabase
    print("✅ Supabase client imported successfully")
except Exception as e:
    print(f"❌ Error importing Supabase client: {str(e)}")
    sys.exit(1)

def check_table_content(table_name: str, limit: int = 5):
    """Check content of a specific table."""
    try:
        print(f"\n📊 Checking {table_name} table...")
        
        # Get count
        count_query = f"SELECT COUNT(*) as count FROM {table_name}"
        count_result = supabase.execute_query(count_query)
        
        if isinstance(count_result, list) and len(count_result) > 0:
            count = count_result[0].get('count', 0)
            print(f"   Total records: {count}")
        else:
            print("   Could not get count")
            count = 0
        
        if count > 0:
            # Get sample records
            sample_query = f"SELECT * FROM {table_name} LIMIT {limit}"
            sample_result = supabase.execute_query(sample_query)
            
            if isinstance(sample_result, list) and len(sample_result) > 0:
                print(f"   Sample records ({len(sample_result)}):")
                for i, record in enumerate(sample_result):
                    print(f"     {i+1}. {record}")
            else:
                print("   No sample records retrieved")
        else:
            print("   Table is empty")
            
        return count
        
    except Exception as e:
        print(f"   ❌ Error checking {table_name}: {str(e)}")
        return 0

def check_vector_search_functions():
    """Check if vector search functions are available."""
    print(f"\n🔍 Checking vector search functions...")
    
    functions_to_check = [
        "search_document_chunks",
        "search_website_chunks",
        "hybrid_search_document_chunks",
        "hybrid_search_website_chunks"
    ]
    
    for func_name in functions_to_check:
        try:
            # Try to call the function with dummy parameters
            result = supabase.rpc(func_name, {
                'query_embedding': [0.1] * 768,  # Dummy embedding
                'similarity_threshold': 0.5,
                'match_count': 1
            }).execute()
            
            if hasattr(result, 'data'):
                print(f"   ✅ {func_name}: Available")
            else:
                print(f"   ❌ {func_name}: Not working")
        except Exception as e:
            print(f"   ❌ {func_name}: Error - {str(e)}")

def main():
    """Main function to check database content."""
    print("🔍 Checking RailGPT Database Content")
    print("=" * 50)
    
    # Check main tables
    tables_to_check = [
        "documents",
        "document_chunks", 
        "websites",
        "website_chunks",
        "document_categories",
        "website_categories"
    ]
    
    total_records = 0
    for table in tables_to_check:
        count = check_table_content(table)
        total_records += count
    
    # Check vector search functions
    check_vector_search_functions()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print(f"   Total records across all tables: {total_records}")
    
    if total_records == 0:
        print("   ⚠️  Database appears to be empty!")
        print("   💡 You may need to upload documents and add websites first.")
    else:
        print("   ✅ Database has content")
    
    return total_records > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
