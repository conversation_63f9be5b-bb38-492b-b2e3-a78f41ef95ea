#!/usr/bin/env python
"""Quick test for the 4 query types."""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server import search_documents_by_content, text_based_website_search, generate_embedding

print("🔍 QUICK TEST OF 4 QUERY TYPES")
print("=" * 50)

# Test 1: ACP full form (should find documents)
print("\n1️⃣ ACP full form (documents only)")
doc_results = search_documents_by_content("full form of ACP", limit=5)
print(f"   📄 Found {len(doc_results)} document chunks")

# Test 2: Rapid response app (should find websites)
print("\n2️⃣ Rapid response app (websites only)")  
web_results = text_based_website_search("rapid response app transportation safety", top_k=5)
print(f"   🌐 Found {len(web_results)} website chunks")

# Test 3: VASP developed (should find both)
print("\n3️⃣ VASP developed (both)")
doc_vasp = search_documents_by_content("VASP developed", limit=5)
web_vasp = text_based_website_search("VASP developed", top_k=5)
print(f"   📄 Documents: {len(doc_vasp)}, 🌐 Websites: {len(web_vasp)}")

# Test 4: WDG4G (should trigger LLM fallback)
print("\n4️⃣ WDG4G details (LLM fallback)")
doc_wdg = search_documents_by_content("WDG4G technical specifications", limit=5) 
web_wdg = text_based_website_search("WDG4G technical specifications", top_k=5)
total_sources = len(doc_wdg) + len(web_wdg)
print(f"   📄 Documents: {len(doc_wdg)}, 🌐 Websites: {len(web_wdg)}")
print(f"   🤖 Should use LLM fallback: {total_sources == 0}")

print("\n✅ Quick test completed")
