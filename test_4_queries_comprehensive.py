#!/usr/bin/env python3
"""
Comprehensive test for the 4 specific query types required by RailGPT:
1. ACP full form (documents only)
2. Rapid response app (websites only) 
3. VASP development (both sources)
4. WDG4G details (LLM fallback)

This test will verify the entire pipeline from database to API response.
"""

import os
import sys
import json
import requests
import time
import logging
from typing import Dict, Any, List

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_QUERIES = [
    {
        "name": "ACP Full Form (Documents Only)",
        "query": "What is the full form of ACP?",
        "expected_source": "document",
        "expected_llm_fallback": False,
        "description": "Should find ACP definition from uploaded documents"
    },
    {
        "name": "Rapid Response App (Websites Only)",
        "query": "Tell me about rapid response app",
        "expected_source": "website",
        "expected_llm_fallback": False,
        "description": "Should find information from rapidresponseapp.com website"
    },
    {
        "name": "VASP Development (Both Sources)",
        "query": "What is VASP development?",
        "expected_source": "both",
        "expected_llm_fallback": False,
        "description": "Should find information from both documents and websites"
    },
    {
        "name": "WDG4G Details (LLM Fallback)",
        "query": "What are WDG4G details?",
        "expected_source": "llm",
        "expected_llm_fallback": True,
        "description": "Should fall back to LLM when no relevant sources found"
    }
]

def check_server_health() -> bool:
    """Check if the backend server is running and healthy."""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Backend server is healthy")
            return True
        else:
            logger.error(f"❌ Backend server health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Cannot connect to backend server: {str(e)}")
        return False

def check_database_content() -> Dict[str, Any]:
    """Check what content is available in the database."""
    try:
        # Check documents
        doc_response = requests.get(f"{API_BASE_URL}/api/documents", timeout=10)
        doc_count = 0
        if doc_response.status_code == 200:
            docs = doc_response.json()
            doc_count = len(docs) if isinstance(docs, list) else 0
        
        # Check websites
        web_response = requests.get(f"{API_BASE_URL}/api/websites", timeout=10)
        web_count = 0
        if web_response.status_code == 200:
            websites = web_response.json()
            web_count = len(websites) if isinstance(websites, list) else 0
        
        # Check document chunks
        chunks_response = requests.get(f"{API_BASE_URL}/api/chunks", timeout=10)
        chunk_count = 0
        if chunks_response.status_code == 200:
            chunks = chunks_response.json()
            chunk_count = len(chunks) if isinstance(chunks, list) else 0
        
        logger.info(f"📊 Database content: {doc_count} documents, {web_count} websites, {chunk_count} chunks")
        
        return {
            "documents": doc_count,
            "websites": web_count,
            "chunks": chunk_count,
            "has_content": doc_count > 0 or web_count > 0 or chunk_count > 0
        }
    except Exception as e:
        logger.error(f"❌ Error checking database content: {str(e)}")
        return {"documents": 0, "websites": 0, "chunks": 0, "has_content": False}

def test_query(query_info: Dict[str, Any]) -> Dict[str, Any]:
    """Test a single query and analyze the response."""
    logger.info(f"🔍 Testing: {query_info['name']}")
    logger.info(f"   Query: {query_info['query']}")
    logger.info(f"   Expected: {query_info['description']}")
    
    try:
        # Make the query request
        response = requests.post(
            f"{API_BASE_URL}/api/query",
            json={"query": query_info["query"]},
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"❌ Query failed with status {response.status_code}: {response.text}")
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}",
                "query": query_info["query"]
            }
        
        result = response.json()
        
        # Analyze the response
        analysis = analyze_query_response(result, query_info)
        
        # Log results
        if analysis["meets_expectations"]:
            logger.info(f"✅ {query_info['name']}: PASSED")
        else:
            logger.warning(f"⚠️  {query_info['name']}: FAILED - {analysis['reason']}")
        
        logger.info(f"   Answer length: {len(result.get('answer', ''))}")
        logger.info(f"   Sources found: {len(result.get('sources', []))}")
        logger.info(f"   LLM fallback used: {result.get('llm_fallback_used', False)}")
        
        return {
            "success": True,
            "query": query_info["query"],
            "response": result,
            "analysis": analysis
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing query '{query_info['query']}': {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "query": query_info["query"]
        }

def analyze_query_response(response: Dict[str, Any], expected: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze if the query response meets expectations."""
    analysis = {
        "meets_expectations": False,
        "reason": "",
        "details": {}
    }
    
    # Check if we got an answer
    answer = response.get("answer", "")
    if not answer or len(answer.strip()) < 10:
        analysis["reason"] = "No meaningful answer provided"
        return analysis
    
    # Check LLM fallback expectation
    llm_fallback_used = response.get("llm_fallback_used", False)
    expected_fallback = expected.get("expected_llm_fallback", False)
    
    if expected_fallback and not llm_fallback_used:
        analysis["reason"] = "Expected LLM fallback but it wasn't used"
        return analysis
    
    if not expected_fallback and llm_fallback_used:
        analysis["reason"] = "LLM fallback was used when sources should have been found"
        return analysis
    
    # Check source expectations
    sources = response.get("sources", [])
    document_sources = response.get("document_sources", [])
    website_sources = response.get("website_sources", [])
    
    expected_source = expected.get("expected_source", "")
    
    if expected_source == "document":
        if not document_sources or len(document_sources) == 0:
            analysis["reason"] = "Expected document sources but none found"
            return analysis
    elif expected_source == "website":
        if not website_sources or len(website_sources) == 0:
            analysis["reason"] = "Expected website sources but none found"
            return analysis
    elif expected_source == "both":
        if (not document_sources or len(document_sources) == 0) and (not website_sources or len(website_sources) == 0):
            analysis["reason"] = "Expected both document and website sources but none found"
            return analysis
    elif expected_source == "llm":
        if not llm_fallback_used:
            analysis["reason"] = "Expected LLM fallback but sources were found instead"
            return analysis
    
    # If we get here, the response meets expectations
    analysis["meets_expectations"] = True
    analysis["reason"] = "Response meets all expectations"
    analysis["details"] = {
        "answer_length": len(answer),
        "total_sources": len(sources),
        "document_sources": len(document_sources),
        "website_sources": len(website_sources),
        "llm_fallback_used": llm_fallback_used
    }
    
    return analysis

def main():
    """Run the comprehensive test suite."""
    logger.info("🚀 Starting RailGPT 4-Query Comprehensive Test")
    logger.info("=" * 60)
    
    # Step 1: Check server health
    if not check_server_health():
        logger.error("❌ Backend server is not available. Please start the server first.")
        return False
    
    # Step 2: Check database content
    db_content = check_database_content()
    if not db_content["has_content"]:
        logger.warning("⚠️  Database appears to be empty. Results may not be meaningful.")
    
    # Step 3: Run all test queries
    results = []
    passed = 0
    failed = 0
    
    for query_info in TEST_QUERIES:
        result = test_query(query_info)
        results.append(result)
        
        if result["success"] and result.get("analysis", {}).get("meets_expectations", False):
            passed += 1
        else:
            failed += 1
        
        # Add a small delay between queries
        time.sleep(1)
    
    # Step 4: Generate summary report
    logger.info("=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info(f"   Total queries tested: {len(TEST_QUERIES)}")
    logger.info(f"   Passed: {passed}")
    logger.info(f"   Failed: {failed}")
    logger.info(f"   Success rate: {(passed/len(TEST_QUERIES)*100):.1f}%")
    
    # Save detailed results
    with open("test_results_4_queries.json", "w") as f:
        json.dump({
            "summary": {
                "total": len(TEST_QUERIES),
                "passed": passed,
                "failed": failed,
                "success_rate": passed/len(TEST_QUERIES)*100
            },
            "database_content": db_content,
            "detailed_results": results
        }, f, indent=2)
    
    logger.info("📄 Detailed results saved to test_results_4_queries.json")
    
    return passed == len(TEST_QUERIES)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
