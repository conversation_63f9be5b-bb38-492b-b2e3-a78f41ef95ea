# RailGPT Local Development Environment Guide

## 🎯 Overview

This guide provides complete instructions for setting up and running the RailGPT application locally for development and testing purposes.

## ✅ Prerequisites

### Required Software
- **Python 3.10+** (Currently using: Python 3.10.16)
- **Node.js 18+** (Currently using: Node.js v22.14.0)
- **Git** (for version control)

### Environment Setup
- All dependencies are already installed
- Environment variables are configured in `.env` file
- Supabase database is set up and accessible

## 🚀 Quick Start

### Option 1: Automated Startup (Recommended)

**For PowerShell users:**
```powershell
.\start-local-dev.ps1
```

**For Command Prompt users:**
```cmd
start-local-dev.bat
```

### Option 2: Manual Startup

**Start Backend Server:**
```powershell
cd backend
python -m uvicorn server:app --host 0.0.0.0 --port 8000 --reload
```

**Start Frontend Server (in a new terminal):**
```powershell
cd frontend
npm start
```

## 🌐 Access URLs

Once both servers are running:

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health

## 🔧 Configuration

### Environment Variables
The application uses the following key environment variables (already configured):

```env
# Supabase Configuration
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co
SUPABASE_KEY=[configured]
SUPABASE_ANON_KEY=[configured]

# AI Model API Keys
GEMINI_API_KEY=[configured]
OPENAI_API_KEY=[configured]
GROQ_API_KEY=[configured]

# Application Settings
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=development
```

### CORS Configuration
The backend is configured to allow requests from:
- http://localhost:3000 (React development server)
- http://127.0.0.1:3000 (Alternative localhost)
- https://railchatbot-cb555.web.app (Production domain)

## 🧪 Testing the Setup

### Automated Testing
Run the comprehensive test suite:
```powershell
powershell -ExecutionPolicy Bypass -File "test-local-setup.ps1"
```

### Manual Testing

**1. Backend Health Check:**
```powershell
Invoke-RestMethod -Uri "http://localhost:8000/api/health" -Method GET
```

**2. Test Query Functionality:**
```powershell
$body = @{ query = "What is railway safety?" } | ConvertTo-Json
Invoke-RestMethod -Uri "http://localhost:8000/api/query" -Method POST -ContentType "application/json" -Body $body
```

**3. Check Environment Variables:**
```powershell
Invoke-RestMethod -Uri "http://localhost:8000/api/env-check" -Method GET
```

## 🎯 Core Features Verified

### ✅ Answer Logic Priority (Working)
The application implements the correct answer priority:
1. **1st Priority**: Uploaded documents ✅ **TESTED & WORKING**
2. **2nd Priority**: Extracted websites ✅ **TESTED & WORKING**
3. **3rd Priority**: LLM model (if no answer found in sources) ✅ **TESTED & WORKING**
4. **Display Logic**: Shows both cards if answers found in multiple sources ✅ **TESTED & WORKING**

### ✅ Database Integration (Working)
- Supabase connection established ✅ **VERIFIED**
- All required tables exist and functional:
  - `documents` and `document_chunks` ✅ **TESTED**
  - `websites` and `website_chunks` ✅ **TESTED**
  - `document_categories` and `website_categories` ✅ **TESTED**

### ✅ API Endpoints (Working)
- Document upload: `/api/upload-document` ✅ **TESTED**
- Website addition: `/api/add-website` ✅ **TESTED**
- Query processing: `/api/query` ✅ **TESTED & WORKING**
- Document viewing: `/api/documents/view/{filename}` ✅ **TESTED**
- Health check: `/api/health` ✅ **TESTED**
- Category management: `/api/categories/` ✅ **FIXED & TESTED**
- Website categories: `/api/categories/website` ✅ **FIXED & TESTED**

## 🔍 Development Features

### Hot Reload
- **Backend**: Uvicorn runs with `--reload` flag for automatic restart on code changes
- **Frontend**: React development server automatically reloads on file changes

### Debugging
- **Backend Logs**: Detailed logging in the terminal running the backend server
- **Frontend Logs**: Browser console and terminal running the frontend server
- **API Documentation**: Available at http://localhost:8000/docs

## 📁 Project Structure

```
RailGPT/
├── backend/                 # FastAPI backend
│   ├── server.py           # Main server file (your preferred entry point)
│   ├── requirements.txt    # Python dependencies
│   ├── config.py          # Configuration management
│   ├── supabase_client.py # Database client
│   └── ...
├── frontend/               # React frontend
│   ├── src/               # Source code
│   ├── package.json       # Node.js dependencies
│   └── ...
├── .env                   # Environment variables
├── start-local-dev.ps1    # PowerShell startup script
├── start-local-dev.bat    # Batch startup script
└── test-local-setup.ps1   # Testing script
```

## 🚀 Ready for GCP Deployment

The local setup mirrors the production environment:
- Same environment variables structure
- Same API endpoints and functionality
- Same database schema and connections
- Production-ready configuration

When ready to deploy to GCP, the local environment can be easily transitioned using the existing deployment scripts.

## 🆘 Troubleshooting

### Frontend Takes Time to Start
- React development server typically takes 1-3 minutes to fully start
- Wait for the message "webpack compiled successfully"

### Port Conflicts
- Backend: Change port in `.env` file (`API_PORT=8000`)
- Frontend: React will automatically suggest an alternative port

### Database Connection Issues
- Verify Supabase credentials in `.env` file
- Check network connectivity
- Test with: `Invoke-RestMethod -Uri "http://localhost:8000/api/env-check"`

## 📞 Support

If you encounter any issues:
1. Check the terminal outputs for error messages
2. Verify all prerequisites are installed
3. Ensure `.env` file exists and is properly configured
4. Run the test script to identify specific issues

---

**🎉 Your RailGPT local development environment is ready!**
