{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import'./App.css';import{sendQuery}from'./services/api';import LLMSelector,{DEFAULT_LLM_MODELS}from'./components/ui/LLMSelector';import InteractiveAnswer from'./components/ui/InteractiveAnswer';import ChatSidebar from'./components/chat/ChatSidebar';import TrainLoader from'./components/ui/TrainLoader';import VisualContent from'./components/ui/VisualContent';import{useChatContext}from'./contexts/ChatContext';// Using ChatMessage interface from services/supabase.ts\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ChatInterface(_ref){var _DEFAULT_LLM_MODELS$f;let{sidebarOpen,setSidebarOpen}=_ref;const{currentSession,messages,createNewChat,loadChatSession,addMessage,updateMessage,clearCurrentChat}=useChatContext();const[input,setInput]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const[activeLLMModel,setActiveLLMModel]=useState('gemini-2.0-flash');// Default LLM model\nconst[showTrainLoader,setShowTrainLoader]=useState(false);const[currentSearchStage,setCurrentSearchStage]=useState('initializing');const messagesEndRef=useRef(null);// Scroll to bottom of chat whenever messages change\nuseEffect(()=>{if(messagesEndRef.current){messagesEndRef.current.scrollIntoView({behavior:'smooth'});}},[messages]);// Helper function to determine if visual content should be shown - SIMPLIFIED\nconst shouldShowVisualContent=(userQuestion,documentAnswer)=>{// ALWAYS show visual content when sources are available\n// Let the user decide if they want to see the visual content or not\nreturn true;};// Helper function to filter visual content - SIMPLIFIED\nconst filterVisualContent=(source,userQuestion)=>{if(typeof source!=='object'){return true;// Show all string sources\n}// Always show all sources - let users decide what's relevant\nreturn true;};// Handle command shortcuts in the textbox\nconst handleCommandShortcut=input=>{// Check if the input is a command\nif(input.startsWith('/')){const command=input.split(' ')[0].toLowerCase();// Command: /model <model-name>\nif(command==='/model'){const modelArg=input.substring(7).trim();const matchedModel=DEFAULT_LLM_MODELS.find(m=>m.name.toLowerCase().includes(modelArg.toLowerCase())||m.id.toLowerCase().includes(modelArg.toLowerCase()));if(matchedModel&&matchedModel.enabled){setActiveLLMModel(matchedModel.id);setInput('');return'processed';}}// Command: /reset or /clear - clear chat history\nelse if(command==='/reset'||command==='/clear'){clearCurrentChat();setInput('');return'processed';}}return'not_processed';};const handleSendMessage=async e=>{e.preventDefault();if(!input.trim()||isSubmitting)return;// Handle command shortcuts like /reset, /model, etc.\nif(input.startsWith('/')){const result=handleCommandShortcut(input);if(result==='processed'){setInput('');return;}// If not processed as a command, continue as a regular message\n}return await sendUserMessage(input);};const sendUserMessage=async messageText=>{var _messagesEndRef$curre;// Create new chat session if none exists\nif(!currentSession){await createNewChat();}const userMessage={id:`user-${Date.now()}`,content:messageText,sender:'user',timestamp:new Date().toISOString(),chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp'};addMessage(userMessage);setInput('');const messageId=Date.now();const tempAiMessage={id:`ai-${messageId}`,content:'',sender:'ai',loading:true,timestamp:new Date().toISOString(),llm_model:activeLLMModel,chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp'};addMessage(tempAiMessage);(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});setIsSubmitting(true);setShowTrainLoader(true);setCurrentSearchStage('initializing');try{var _messagesEndRef$curre2,_messagesEndRef$curre3;(_messagesEndRef$curre2=messagesEndRef.current)===null||_messagesEndRef$curre2===void 0?void 0:_messagesEndRef$curre2.scrollIntoView({behavior:'smooth'});// Simulate search progress updates with train loader\nsetTimeout(()=>setCurrentSearchStage('searching_documents'),500);setTimeout(()=>setCurrentSearchStage('searching_websites'),2000);setTimeout(()=>setCurrentSearchStage('generating_answer'),4000);const response=await sendQuery(messageText,activeLLMModel);// Create the AI message based on strict priority logic\nconst aiMessage={id:`ai-${messageId}`,content:response.answer,document_answer:response.document_answer,website_answer:response.website_answer,llm_model:response.llm_model||activeLLMModel,sender:'ai',sources:response.sources,document_sources:response.document_sources,website_sources:response.website_sources,chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp',llm_fallback_used:response.llm_fallback_used// CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n};updateMessage(tempAiMessage.id,aiMessage);(_messagesEndRef$curre3=messagesEndRef.current)===null||_messagesEndRef$curre3===void 0?void 0:_messagesEndRef$curre3.scrollIntoView({behavior:'smooth'});}catch(error){console.error('Error sending message:',error);// Provide more helpful error message without the specific query\nconst errorMessage={id:`ai-${messageId}`,content:`I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,sender:'ai',chatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'temp',llm_fallback_used:true// CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n};updateMessage(tempAiMessage.id,errorMessage);}finally{setIsSubmitting(false);setShowTrainLoader(false);setCurrentSearchStage('complete');}};const processDocumentSources=sources=>{if(!sources||sources.length===0)return[];// Improved grouping by filename to avoid repetition while preserving all valid sources\nconst groupedSources={};sources.forEach(source=>{let filename;let page;if(typeof source==='string'){// Parse string format like \"MaintenanceManual.pdf – Page 3\"\nconst match=source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);filename=match?match[1].trim():source;page=match&&match[2]?parseInt(match[2]):1;}else{filename=source.name||source.filename||\"Unknown Document\";// Support multi-page list coming from backend\nif(source.pages&&Array.isArray(source.pages)&&source.pages.length>0){page=source.pages;}else{page=source.page||1;}}if(!groupedSources[filename]){groupedSources[filename]={filename,pages:new Set()};}const addPage=p=>{if(p&&p>0){// Only add valid page numbers\ngroupedSources[filename].pages.add(p);}};if(Array.isArray(page)){page.forEach(addPage);}else{addPage(page);}});// Convert to display format with viewer links\nreturn Object.values(groupedSources).map(group=>{const sortedPages=Array.from(group.pages).sort((a,b)=>a-b);const pageText=sortedPages.length===1?`Page ${sortedPages[0]}`:`Pages ${sortedPages.join(', ')}`;// Create link for document viewer that opens at exact page number\nreturn{text:`${group.filename} – ${pageText}`,link:`/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,isDocument:true};});};const processWebsiteSources=sources=>{if(!sources||sources.length===0)return[];// Use Map for better duplicate detection and preserve order\nconst uniqueUrls=new Map();sources.forEach(source=>{let url;let displayText;if(typeof source==='string'){url=source.startsWith('http')?source:`https://${source}`;try{const urlObj=new URL(url);displayText=urlObj.hostname.replace(/^www\\./,'');}catch{displayText=source;}}else{url=source.url||'https://railgpt.indianrailways.gov.in';try{const urlObj=new URL(url);displayText=urlObj.hostname.replace(/^www\\./,'');}catch{displayText=url;}}// Only add if not already present\nif(!uniqueUrls.has(url)){uniqueUrls.set(url,{text:displayText,link:url,isDocument:false// Mark as website source\n});}});return Array.from(uniqueUrls.values());// Return all unique website sources\n};// Component for expandable source list with appropriate click behaviors\nconst SourceList=_ref2=>{let{items,maxVisible=3}=_ref2;const[expanded,setExpanded]=useState(items.length<=maxVisible);if(items.length===0)return null;const visibleItems=expanded?items:items.slice(0,maxVisible);const hasMore=items.length>maxVisible;const handleDocumentClick=(e,link)=>{// If we want to handle document links in a special way, we can do so here\n// For example, we could open a modal or new tab with the document viewer\n// Currently, just allowing regular link behavior\n};return/*#__PURE__*/_jsxs(\"ul\",{className:\"text-xs list-disc pl-4 mt-1 space-y-1\",children:[visibleItems.map((item,index)=>/*#__PURE__*/_jsx(\"li\",{children:item.link?/*#__PURE__*/_jsx(\"a\",{href:item.link,target:\"_blank\",rel:\"noopener noreferrer\",className:\"hover:underline text-blue-600 transition-colors duration-200\",title:item.isDocument?\"Open document at this page\":\"Open website in new tab\",onClick:item.isDocument?e=>handleDocumentClick(e,item.link):undefined,children:item.text}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:item.text})},index)),hasMore&&!expanded&&/*#__PURE__*/_jsx(\"li\",{className:\"list-none\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setExpanded(true),className:\"text-blue-500 hover:underline text-xs transition-colors duration-200\",children:[\"+ \",items.length-maxVisible,\" more sources\"]})}),hasMore&&expanded&&/*#__PURE__*/_jsx(\"li\",{className:\"list-none\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>setExpanded(false),className:\"text-blue-500 hover:underline text-xs transition-colors duration-200\",children:\"Show less\"})})]});};// Sidebar handlers\nconst handleChatSelect=async chatSession=>{// Load the selected chat session using context\nawait loadChatSession(chatSession.id);setActiveLLMModel(chatSession.model_used||'gemini-2.0-flash');setSidebarOpen(false);// Close sidebar on mobile after selection\n};const handleNewChat=async()=>{console.log('Creating new chat...');await createNewChat();setSidebarOpen(false);// Close sidebar on mobile after creating new chat\n};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-screen bg-gray-100 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(TrainLoader,{isVisible:showTrainLoader,message:(()=>{switch(currentSearchStage){case'searching_documents':return\"RailGPT Searching in Documents...\";case'searching_websites':return\"RailGPT Searching in Websites...\";case'generating_answer':return\"RailGPT Generating Response...\";default:return\"RailGPT Processing Your Query...\";}})(),trainType:\"express\",currentStage:currentSearchStage,sidebarOpen:sidebarOpen}),/*#__PURE__*/_jsx(ChatSidebar,{isOpen:sidebarOpen,onToggle:()=>setSidebarOpen(!sidebarOpen),currentChatId:(currentSession===null||currentSession===void 0?void 0:currentSession.id)||'',onChatSelect:handleChatSelect,onNewChat:handleNewChat}),/*#__PURE__*/_jsxs(\"div\",{className:`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen?'lg:ml-80':''}`,children:[/*#__PURE__*/_jsx(\"div\",{className:`flex-1 ${messages.length>0?'overflow-y-auto':'overflow-hidden'} p-4 pb-32`,children:messages.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-gray-500\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xl font-semibold mb-3\",children:\"Welcome to RailGPT!\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Ask questions about Indian Railways...\"})]})}):/*#__PURE__*/_jsxs(\"div\",{children:[messages.map(message=>/*#__PURE__*/_jsx(\"div\",{className:`mb-4 ${message.sender==='user'?'flex justify-end':'flex justify-start'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${message.sender==='user'?'bg-blue-500 text-white':'bg-white text-gray-800 shadow-md'}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-start mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:message.sender==='user'?'You':'RailGPT'}),message.timestamp&&/*#__PURE__*/_jsx(\"span\",{className:`text-xs ml-2 ${message.sender==='user'?'text-blue-100':'text-gray-500'}`,children:new Date(message.timestamp).toLocaleTimeString()})]}),message.sender==='user'&&message.content&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 whitespace-pre-wrap\",children:message.content}),message.sender==='ai'&&/*#__PURE__*/_jsx(\"div\",{children:((_message$document_ans,_message$website_answ,_message$document_sou,_message$website_sour)=>{// Only hide if this specific message is loading AND has no content yet\nif(message.loading&&showTrainLoader&&!message.content&&!message.document_answer&&!message.website_answer){return null;}// Process sources with improved deduplication\nconst documentSourceItems=processDocumentSources(message.document_sources);const websiteSourceItems=processWebsiteSources(message.website_sources);// SIMPLIFIED RAILGPT PRIORITY LOGIC: Check sources first, then content\n// Always show sources when they exist, regardless of content length\nconst hasDocumentSources=documentSourceItems.length>0;const hasWebsiteSources=websiteSourceItems.length>0;const hasDocumentContent=!!(message.document_answer&&message.document_answer.trim()!==\"\");const hasWebsiteContent=!!(message.website_answer&&message.website_answer.trim()!==\"\");// LLM fallback when explicitly flagged OR when no sources exist\nconst hasLLMFallback=message.llm_fallback_used||!hasDocumentSources&&!hasWebsiteSources;// Enhanced debug logging for rendering\nconsole.log(`🔍 [SOURCE-DEBUG] Rendering message ${message.id}:`,{hasDocumentSources,hasWebsiteSources,hasDocumentContent,hasWebsiteContent,hasLLMFallback,llm_fallback_used:message.llm_fallback_used,documentAnswerLength:((_message$document_ans=message.document_answer)===null||_message$document_ans===void 0?void 0:_message$document_ans.length)||0,websiteAnswerLength:((_message$website_answ=message.website_answer)===null||_message$website_answ===void 0?void 0:_message$website_answ.length)||0,documentSourcesCount:documentSourceItems.length,websiteSourcesCount:websiteSourceItems.length,rawDocumentAnswer:message.document_answer?'EXISTS':'MISSING',rawWebsiteAnswer:message.website_answer?'EXISTS':'MISSING',rawDocumentSources:((_message$document_sou=message.document_sources)===null||_message$document_sou===void 0?void 0:_message$document_sou.length)||0,rawWebsiteSources:((_message$website_sour=message.website_sources)===null||_message$website_sour===void 0?void 0:_message$website_sour.length)||0,renderingDecision:hasDocumentSources?'DOCUMENT_PRIORITY':hasWebsiteSources?'WEBSITE_PRIORITY':'LLM_FALLBACK'});// Get the user's question for context - find the most recent user message before this AI message\nconst currentMessageIndex=messages.findIndex(m=>m.id===message.id);let userQuestion='';// Look backwards from current AI message to find the most recent user message\nfor(let i=currentMessageIndex-1;i>=0;i--){if(messages[i].sender==='user'&&messages[i].content){userQuestion=messages[i].content;break;}}console.log('🔍 DEBUG: Found user question for AI message:',{aiMessageId:message.id,userQuestion});// Conditional display logic based on answer sources\nconst components=[];// eslint-disable-next-line @typescript-eslint/no-unused-vars\nlet answerSource='';// SIMPLIFIED RAILGPT PRIORITY SYSTEM: Show sources when they exist\n// PRIORITY 1: Document Results (Highest Priority) - Show if sources exist\nif(hasDocumentSources){answerSource='document_only';components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-blue-800 text-sm mb-3 flex items-center\",children:[\"\\uD83D\\uDCC4 Answer Found in \",documentSourceItems.length>1?'Documents':'Document']}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.document_answer||message.content||\"Information found in documents (see sources below).\",query:userQuestion,model:message.llm_model,chatId:message.chatId}),(_message$document_sou2=>{const visualSources=((_message$document_sou2=message.document_sources)===null||_message$document_sou2===void 0?void 0:_message$document_sou2.filter(source=>typeof source==='object'&&source.visual_content))||[];console.log('🔍 Visual sources found:',visualSources.length);console.log('🔍 Visual sources:',visualSources.map(s=>({filename:s.filename,content_type:s.content_type,has_visual_content:!!s.visual_content})));// First try to find sources that match the user's specific request\nlet relevantSources=visualSources.filter(source=>filterVisualContent(source,userQuestion));console.log('🎯 Relevant sources after filtering:',relevantSources.length);// If no specific matches and user asked for images OR logos, show any available images\nif(relevantSources.length===0&&(userQuestion.toLowerCase().includes('image')||userQuestion.toLowerCase().includes('logo'))){relevantSources=visualSources.filter(source=>source.content_type==='image');console.log('🔍 DEBUG: No specific matches, showing all available images for image/logo query:',relevantSources.length);}return relevantSources.length>0&&shouldShowVisualContent(userQuestion,message.document_answer||'')?/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"text-sm font-semibold text-blue-800 mb-3\",children:\"\\uD83D\\uDCCA Visual Content:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:relevantSources.map((source,index)=>/*#__PURE__*/_jsx(VisualContent,{source:source},index))})]}):null;})(),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 pt-3 border-t border-blue-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-blue-700 font-semibold mb-1\",children:\"Sources:\"}),/*#__PURE__*/_jsx(SourceList,{items:documentSourceItems,maxVisible:3})]})]},\"document-priority\"));// PRIORITY 2: Website Results (Medium Priority - Only if no document sources)\n}else if(hasWebsiteSources){answerSource='website_only';const websiteLabel=websiteSourceItems.length>1?'Extracted Websites':'Extracted Website';components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-green-800 text-sm mb-3 flex items-center\",children:[\"\\uD83C\\uDF10 Answer Found in \",websiteLabel]}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.website_answer||message.content||\"Information found in websites (see sources below).\",query:userQuestion,model:message.llm_model,chatId:message.chatId}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 pt-3 border-t border-green-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-green-700 font-semibold mb-1\",children:\"Sources:\"}),/*#__PURE__*/_jsx(SourceList,{items:websiteSourceItems,maxVisible:3})]})]},\"website-priority\"));// PRIORITY 3: LLM Fallback (Lowest Priority - Only if no relevant chunks)\n}else if(hasLLMFallback){var _message$llm_model,_message$llm_model2,_message$llm_model3,_message$llm_model4,_message$llm_model5,_message$llm_model6,_message$llm_model7;answerSource='llm_fallback';const modelName=message.llm_model||'Qwen';// Default to Qwen as specified\nconst modelLogo=(_message$llm_model=message.llm_model)!==null&&_message$llm_model!==void 0&&_message$llm_model.includes('chatgpt')?'🤖':(_message$llm_model2=message.llm_model)!==null&&_message$llm_model2!==void 0&&_message$llm_model2.includes('groq')?'⚡':(_message$llm_model3=message.llm_model)!==null&&_message$llm_model3!==void 0&&_message$llm_model3.includes('deepseek')?'🔍':(_message$llm_model4=message.llm_model)!==null&&_message$llm_model4!==void 0&&_message$llm_model4.includes('qwen')?'🧠':(_message$llm_model5=message.llm_model)!==null&&_message$llm_model5!==void 0&&_message$llm_model5.includes('ollama')?'🏠':(_message$llm_model6=message.llm_model)!==null&&_message$llm_model6!==void 0&&_message$llm_model6.includes('huggingface')?'🤗':(_message$llm_model7=message.llm_model)!==null&&_message$llm_model7!==void 0&&_message$llm_model7.includes('gemini')?'💎':'🤖';// Only show the LLM fallback card if not in loading state\nif(!showTrainLoader||!message.loading){components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-purple-800 text-sm mb-3 flex items-center\",children:[modelLogo,\" Answer Generated by \",modelName]}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.content||\"I couldn't find any relevant information to answer your question.\",query:userQuestion,model:message.llm_model||'Qwen',chatId:message.chatId}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-3 pt-3 border-t border-purple-200\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-purple-600 italic\",children:\"\\u26A0\\uFE0F No matching information found in uploaded documents or extracted websites.\"})})]},\"llm-fallback\"));}// Case 5: No sources found and fallback disabled (or similar edge case)\n}else{// eslint-disable-next-line @typescript-eslint/no-unused-vars\nanswerSource='no_results';// Only show the \"no results\" card if not in loading state\nif(!showTrainLoader||!message.loading){components.push(/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-2\",children:\"No sources found\"}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.content||\"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\",query:userQuestion,model:message.llm_model||'Qwen',chatId:message.chatId})]},\"no-results\"));}}// If we have components to display, render them\nif(components.length>0){return/*#__PURE__*/_jsx(\"div\",{className:\"mt-3\",children:components});}// Fallback for any unhandled edge cases (should rarely happen)\nconsole.warn(\"Frontend: Unhandled rendering case\");return/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-2\",children:\"Rendering Error\"}),/*#__PURE__*/_jsx(InteractiveAnswer,{content:message.content||\"An error occurred while rendering the response.\",query:userQuestion,model:message.llm_model||'Gemini',chatId:message.chatId})]});})()})]})},message.id)),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef,style:{float:'left',clear:'both'}})]})}),/*#__PURE__*/_jsxs(\"div\",{className:`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen?'lg:left-80 left-0':'left-0'}`,children:[/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSendMessage,className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 relative flex\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:input,onChange:e=>{const newValue=e.target.value;setInput(newValue);// Don't handle command shortcuts as you type, only on submit\n},placeholder:\"Type your message... (/model, /reset, /clear)\",className:\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",disabled:isSubmitting,\"aria-label\":\"Message input\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(LLMSelector,{currentModel:activeLLMModel,onModelChange:modelId=>setActiveLLMModel(modelId),isLoading:isSubmitting}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isSubmitting||!input.trim(),className:\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\",title:\"Send message\",children:/*#__PURE__*/_jsx(\"span\",{children:isSubmitting?\"Sending...\":\"Send\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-400 mt-1 text-center\",children:[\"Current model: \",((_DEFAULT_LLM_MODELS$f=DEFAULT_LLM_MODELS.find(m=>m.id===activeLLMModel))===null||_DEFAULT_LLM_MODELS$f===void 0?void 0:_DEFAULT_LLM_MODELS$f.name)||activeLLMModel]})]}),messages.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"h-36\"})]})]});}function App(_ref3){let{sidebarOpen,setSidebarOpen}=_ref3;return/*#__PURE__*/_jsx(ChatInterface,{sidebarOpen:sidebarOpen,setSidebarOpen:setSidebarOpen});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "LLMSelector", "DEFAULT_LLM_MODELS", "InteractiveAnswer", "ChatSidebar", "TrainLoader", "VisualContent", "useChatContext", "jsx", "_jsx", "jsxs", "_jsxs", "ChatInterface", "_ref", "_DEFAULT_LLM_MODELS$f", "sidebarOpen", "setSidebarOpen", "currentSession", "messages", "createNewChat", "loadChatSession", "addMessage", "updateMessage", "clearCurrentChat", "input", "setInput", "isSubmitting", "setIsSubmitting", "activeLLMModel", "setActiveLLMModel", "showTrainLoader", "setShowTrainLoader", "currentSearchStage", "setCurrentSearchStage", "messagesEndRef", "current", "scrollIntoView", "behavior", "shouldShowVisualContent", "userQuestion", "documentAnswer", "filterVisualContent", "source", "handleCommandShortcut", "startsWith", "command", "split", "toLowerCase", "modelArg", "substring", "trim", "matchedModel", "find", "m", "name", "includes", "id", "enabled", "handleSendMessage", "e", "preventDefault", "result", "sendUserMessage", "messageText", "_messagesEndRef$curre", "userMessage", "Date", "now", "content", "sender", "timestamp", "toISOString", "chatId", "messageId", "tempAiMessage", "loading", "llm_model", "_messagesEndRef$curre2", "_messagesEndRef$curre3", "setTimeout", "response", "aiMessage", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "llm_fallback_used", "error", "console", "errorMessage", "processDocumentSources", "length", "groupedSources", "for<PERSON>ach", "filename", "page", "match", "parseInt", "pages", "Array", "isArray", "Set", "addPage", "p", "add", "Object", "values", "map", "group", "sortedPages", "from", "sort", "a", "b", "pageText", "join", "text", "link", "encodeURIComponent", "isDocument", "processWebsiteSources", "uniqueUrls", "Map", "url", "displayText", "url<PERSON>bj", "URL", "hostname", "replace", "has", "set", "SourceList", "_ref2", "items", "maxVisible", "expanded", "setExpanded", "visibleItems", "slice", "hasMore", "handleDocumentClick", "className", "children", "item", "index", "href", "target", "rel", "title", "onClick", "undefined", "handleChatSelect", "chatSession", "model_used", "handleNewChat", "log", "isVisible", "message", "trainType", "currentStage", "isOpen", "onToggle", "currentChatId", "onChatSelect", "onNewChat", "toLocaleTimeString", "_message$document_ans", "_message$website_answ", "_message$document_sou", "_message$website_sour", "documentSourceItems", "websiteSourceItems", "hasDocumentSources", "hasWebsiteSources", "hasDocumentContent", "has<PERSON>ebsite<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentAnswerLength", "websiteAnswerLength", "documentSourcesCount", "websiteSourcesCount", "rawDocumentAnswer", "rawWebsiteAnswer", "rawDocumentSources", "rawWebsiteSources", "renderingDecision", "currentMessageIndex", "findIndex", "i", "aiMessageId", "components", "answerSource", "push", "query", "model", "_message$document_sou2", "visualSources", "filter", "visual_content", "s", "content_type", "has_visual_content", "relevantSources", "websiteLabel", "_message$llm_model", "_message$llm_model2", "_message$llm_model3", "_message$llm_model4", "_message$llm_model5", "_message$llm_model6", "_message$llm_model7", "modelName", "modelLogo", "warn", "ref", "style", "float", "clear", "onSubmit", "type", "value", "onChange", "newValue", "placeholder", "disabled", "currentModel", "onModelChange", "modelId", "isLoading", "App", "_ref3"], "sources": ["C:/IR App/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\nimport { ChatSession, ChatMessage } from './services/supabase';\nimport { Source, ProcessedSource } from './types/sources';\n\n// Using ChatMessage interface from services/supabase.ts\n\ninterface ChatInterfaceProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n\n  const [input, setInput] = useState<string>('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown - SIMPLIFIED\n  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {\n    // ALWAYS show visual content when sources are available\n    // Let the user decide if they want to see the visual content or not\n    return true;\n  };\n\n  // Helper function to filter visual content - SIMPLIFIED\n  const filterVisualContent = (source: Source | string, userQuestion: string) => {\n    if (typeof source !== 'object') {\n      return true; // Show all string sources\n    }\n\n    // Always show all sources - let users decide what's relevant\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = (input: string) => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m =>\n          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||\n          m.id.toLowerCase().includes(modelArg.toLowerCase())\n        );\n\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n\n    return 'not_processed';\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n\n    return await sendUserMessage(input);\n  };\n\n  const sendUserMessage = async (messageText: string) => {\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(userMessage);\n    setInput('');\n\n    const messageId = Date.now();\n    const tempAiMessage: ChatMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(tempAiMessage);\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n\n    try {\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: currentSession?.id || 'temp',\n        llm_fallback_used: response.llm_fallback_used,  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n      };\n\n      updateMessage(tempAiMessage.id, aiMessage);\n\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: currentSession?.id || 'temp',\n        llm_fallback_used: true,  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n      };\n\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n\n  const processDocumentSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Improved grouping by filename to avoid repetition while preserving all valid sources\n    const groupedSources: { [key: string]: { filename: string; pages: Set<number> } } = {};\n\n    sources.forEach(source => {\n      let filename: string;\n      let page: number | number[];\n\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        // Support multi-page list coming from backend\n        if ((source as any).pages && Array.isArray((source as any).pages) && (source as any).pages.length > 0) {\n          page = (source as any).pages as number[];\n        } else {\n          page = source.page || 1;\n        }\n      }\n\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = { filename, pages: new Set<number>() };\n      }\n\n      const addPage = (p: number) => {\n        if (p && p > 0) { // Only add valid page numbers\n          groupedSources[filename].pages.add(p);\n        }\n      };\n\n      if (Array.isArray(page)) {\n        page.forEach(addPage);\n      } else {\n        addPage(page as number);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = Array.from(group.pages).sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1\n        ? `Page ${sortedPages[0]}`\n        : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n\n  const processWebsiteSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Use Map for better duplicate detection and preserve order\n    const uniqueUrls = new Map<string, { text: string; link: string; isDocument?: boolean }>();\n\n    sources.forEach(source => {\n      let url: string;\n      let displayText: string;\n\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n\n      // Only add if not already present\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.set(url, {\n          text: displayText,\n          link: url,\n          isDocument: false  // Mark as website source\n        });\n      }\n    });\n\n    return Array.from(uniqueUrls.values()); // Return all unique website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({ items, maxVisible = 3 }: {\n    items: ProcessedSource[];\n    maxVisible?: number\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n\n    if (items.length === 0) return null;\n\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n\n    const handleDocumentClick = (e: React.MouseEvent<HTMLAnchorElement>, link: string) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n\n    return (\n      <ul className=\"text-xs list-disc pl-4 mt-1 space-y-1\">\n        {visibleItems.map((item, index) => (\n              <li key={index}>\n                {item.link ? (\n              <a\n                href={item.link}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"hover:underline text-blue-600 transition-colors duration-200\"\n                title={item.isDocument ? \"Open document at this page\" : \"Open website in new tab\"}\n                onClick={item.isDocument ? (e) => handleDocumentClick(e, item.link!) : undefined}\n              >\n                    {item.text}\n                  </a>\n                ) : (\n              <span className=\"text-gray-700\">{item.text}</span>\n                )}\n              </li>\n            ))}\n        {hasMore && !expanded && (\n          <li className=\"list-none\">\n            <button\n              onClick={() => setExpanded(true)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n            >\n              + {items.length - maxVisible} more sources\n            </button>\n          </li>\n        )}\n        {hasMore && expanded && (\n          <li className=\"list-none\">\n              <button\n                onClick={() => setExpanded(false)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n              >\n                Show less\n              </button>\n            </li>\n                )}\n      </ul>\n    );\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async (chatSession: ChatSession) => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 transition-colors duration-300\">\n      {/* Train Loader Overlay */}\n      <TrainLoader\n        isVisible={showTrainLoader}\n        message={(() => {\n          switch (currentSearchStage) {\n            case 'searching_documents':\n              return \"RailGPT Searching in Documents...\";\n            case 'searching_websites':\n              return \"RailGPT Searching in Websites...\";\n            case 'generating_answer':\n              return \"RailGPT Generating Response...\";\n            default:\n              return \"RailGPT Processing Your Query...\";\n          }\n        })()}\n        trainType=\"express\"\n        currentStage={currentSearchStage}\n        sidebarOpen={sidebarOpen}\n      />\n\n      {/* Chat Sidebar */}\n      <ChatSidebar\n        isOpen={sidebarOpen}\n        onToggle={() => setSidebarOpen(!sidebarOpen)}\n        currentChatId={currentSession?.id || ''}\n        onChatSelect={handleChatSelect}\n        onNewChat={handleNewChat}\n      />\n\n      {/* Main Chat Area */}\n      <div className={`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`}>\n        {/* Message Area - only scrollable when messages exist */}\n        <div className={`flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`}>\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center text-gray-500\">\n                <p className=\"text-xl font-semibold mb-3\">Welcome to RailGPT!</p>\n                <p>Ask questions about Indian Railways...</p>\n              </div>\n            </div>\n          ) : (\n            <div>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`mb-4 ${\n                    message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'\n                  }`}\n                >\n                  <div\n                    className={`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${\n                      message.sender === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-white text-gray-800 shadow-md'\n                    }`}\n                  >\n                    <div className=\"flex justify-between items-start mb-1\">\n                      <span className=\"font-semibold\">\n                        {message.sender === 'user' ? 'You' : 'RailGPT'}\n                      </span>\n                      {message.timestamp && (\n                        <span className={`text-xs ml-2 ${\n                          message.sender === 'user'\n                            ? 'text-blue-100'\n                            : 'text-gray-500'\n                        }`}>\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                      )}\n                    </div>\n\n                    {/* Only show content directly for user messages */}\n                    {message.sender === 'user' && message.content && (\n                      <div className=\"mt-2 whitespace-pre-wrap\">{message.content}</div>\n                    )}\n\n                    {/* AI messages with strict priority display logic */}\n                    {message.sender === 'ai' && (\n                      <div>\n                        {(() => {\n                          // Only hide if this specific message is loading AND has no content yet\n                          if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                            return null;\n                          }\n\n                          // Process sources with improved deduplication\n                          const documentSourceItems = processDocumentSources(message.document_sources);\n                          const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                          // SIMPLIFIED RAILGPT PRIORITY LOGIC: Check sources first, then content\n                          // Always show sources when they exist, regardless of content length\n                          const hasDocumentSources = documentSourceItems.length > 0;\n                          const hasWebsiteSources = websiteSourceItems.length > 0;\n                          const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                          const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                          // LLM fallback when explicitly flagged OR when no sources exist\n                          const hasLLMFallback = message.llm_fallback_used || (!hasDocumentSources && !hasWebsiteSources);\n\n                          // Enhanced debug logging for rendering\n                          console.log(`🔍 [SOURCE-DEBUG] Rendering message ${message.id}:`, {\n                            hasDocumentSources,\n                            hasWebsiteSources,\n                            hasDocumentContent,\n                            hasWebsiteContent,\n                            hasLLMFallback,\n                            llm_fallback_used: message.llm_fallback_used,\n                            documentAnswerLength: message.document_answer?.length || 0,\n                            websiteAnswerLength: message.website_answer?.length || 0,\n                            documentSourcesCount: documentSourceItems.length,\n                            websiteSourcesCount: websiteSourceItems.length,\n                            rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                            rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                            rawDocumentSources: message.document_sources?.length || 0,\n                            rawWebsiteSources: message.website_sources?.length || 0,\n                            renderingDecision: hasDocumentSources ? 'DOCUMENT_PRIORITY' : hasWebsiteSources ? 'WEBSITE_PRIORITY' : 'LLM_FALLBACK'\n                          });\n\n                          // Get the user's question for context - find the most recent user message before this AI message\n                          const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                          let userQuestion = '';\n                          \n                          // Look backwards from current AI message to find the most recent user message\n                          for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                            if (messages[i].sender === 'user' && messages[i].content) {\n                              userQuestion = messages[i].content;\n                              break;\n                            }\n                          }\n                          \n                          console.log('🔍 DEBUG: Found user question for AI message:', { aiMessageId: message.id, userQuestion });\n\n                          // Conditional display logic based on answer sources\n                          const components = [];\n                          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                          let answerSource = '';\n\n                          // SIMPLIFIED RAILGPT PRIORITY SYSTEM: Show sources when they exist\n\n                          // PRIORITY 1: Document Results (Highest Priority) - Show if sources exist\n                          if (hasDocumentSources) {\n                            answerSource = 'document_only';\n\n                            components.push(\n                              <div key=\"document-priority\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer Found in {documentSourceItems.length > 1 ? 'Documents' : 'Document'}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || message.content || \"Information found in documents (see sources below).\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  console.log('🔍 Visual sources found:', visualSources.length);\n                                  console.log('🔍 Visual sources:', visualSources.map(s => ({\n                                    filename: (s as Source).filename,\n                                    content_type: (s as Source).content_type,\n                                    has_visual_content: !!(s as Source).visual_content\n                                  })));\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  console.log('🎯 Relevant sources after filtering:', relevantSources.length);\n                                  \n                                  // If no specific matches and user asked for images OR logos, show any available images\n                                  if (relevantSources.length === 0 && (userQuestion.toLowerCase().includes('image') || userQuestion.toLowerCase().includes('logo'))) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific matches, showing all available images for image/logo query:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // PRIORITY 2: Website Results (Medium Priority - Only if no document sources)\n                          } else if (hasWebsiteSources) {\n                            answerSource = 'website_only';\n\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-priority\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer Found in {websiteLabel}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || message.content || \"Information found in websites (see sources below).\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // PRIORITY 3: LLM Fallback (Lowest Priority - Only if no relevant chunks)\n                          } else if (hasLLMFallback) {\n                            answerSource = 'llm_fallback';\n\n                            const modelName = message.llm_model || 'Qwen';  // Default to Qwen as specified\n                            const modelLogo = message.llm_model?.includes('chatgpt') ? '🤖' :\n                                            message.llm_model?.includes('groq') ? '⚡' :\n                                            message.llm_model?.includes('deepseek') ? '🔍' :\n                                            message.llm_model?.includes('qwen') ? '🧠' :\n                                            message.llm_model?.includes('ollama') ? '🏠' :\n                                            message.llm_model?.includes('huggingface') ? '🤗' :\n                                            message.llm_model?.includes('gemini') ? '💎' : '🤖';\n\n                            // Only show the LLM fallback card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"llm-fallback\" className=\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-purple-800 text-sm mb-3 flex items-center\">\n                                  {modelLogo} Answer Generated by {modelName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't find any relevant information to answer your question.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Qwen'}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-purple-200\">\n                                  <p className=\"text-xs text-purple-600 italic\">\n                                    ⚠️ No matching information found in uploaded documents or extracted websites.\n                                  </p>\n                                </div>\n                              </div>\n                            );\n                            }\n\n                          // Case 5: No sources found and fallback disabled (or similar edge case)\n                          } else {\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            answerSource = 'no_results';\n\n                            // Only show the \"no results\" card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"no-results\" className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                                <p className=\"text-sm text-gray-600 mb-2\">No sources found</p>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Qwen'}\n                                  chatId={message.chatId}\n                                />\n                              </div>\n                            );\n                            }\n                          }\n\n                          // If we have components to display, render them\n                          if (components.length > 0) {\n                            return (\n                              <div className=\"mt-3\">\n                                {components}\n                              </div>\n                            );\n                          }\n\n                          // Fallback for any unhandled edge cases (should rarely happen)\n                          console.warn(\"Frontend: Unhandled rendering case\");\n                          return (\n                            <div className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                              <p className=\"text-sm text-gray-600 mb-2\">Rendering Error</p>\n                              <InteractiveAnswer\n                                content={message.content || \"An error occurred while rendering the response.\"}\n                                query={userQuestion}\n                                model={message.llm_model || 'Gemini'}\n                                chatId={message.chatId}\n                              />\n                            </div>\n                          );\n                        })()}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n              <div ref={messagesEndRef} style={{ float: 'left', clear: 'both' }} />\n            </div>\n          )}\n        </div>\n\n        {/* Fixed Chat Input Box at Bottom - using fixed positioning */}\n        <div className={`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`}>\n          <form onSubmit={handleSendMessage} className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 relative flex\">\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => {\n                  const newValue = e.target.value;\n                  setInput(newValue);\n                  // Don't handle command shortcuts as you type, only on submit\n                }}\n                placeholder=\"Type your message... (/model, /reset, /clear)\"\n                className=\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\n                disabled={isSubmitting}\n                aria-label=\"Message input\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <LLMSelector\n                currentModel={activeLLMModel}\n                onModelChange={(modelId) => setActiveLLMModel(modelId)}\n                isLoading={isSubmitting}\n              />\n              <button\n                type=\"submit\"\n                disabled={isSubmitting || !input.trim()}\n                className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\"\n                title=\"Send message\"\n              >\n                <span>{isSubmitting ? \"Sending...\" : \"Send\"}</span>\n              </button>\n            </div>\n          </form>\n                      <div className=\"text-xs text-gray-400 mt-1 text-center\">\n            Current model: {DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)?.name || activeLLMModel}\n          </div>\n        </div>\n        {/* Spacer div to push content up above the fixed input box - only needed when there are messages */}\n        {messages.length > 0 && <div className=\"h-36\"></div>}\n      </div>\n    </div>\n  );\n}\n\ninterface AppProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction App({ sidebarOpen, setSidebarOpen }: AppProps) {\n  return (\n    <ChatInterface sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,WAAW,CAClB,OAASC,SAAS,KAAQ,gBAAgB,CAC1C,MAAO,CAAAC,WAAW,EAAIC,kBAAkB,KAAQ,6BAA6B,CAC7E,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,WAAW,KAAM,+BAA+B,CACvD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,OAASC,cAAc,KAAQ,wBAAwB,CAIvD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,QAAS,CAAAC,aAAaA,CAAAC,IAAA,CAAsD,KAAAC,qBAAA,IAArD,CAAEC,WAAW,CAAEC,cAAmC,CAAC,CAAAH,IAAA,CACxE,KAAM,CACJI,cAAc,CACdC,QAAQ,CACRC,aAAa,CACbC,eAAe,CACfC,UAAU,CACVC,aAAa,CACbC,gBACF,CAAC,CAAGhB,cAAc,CAAC,CAAC,CAEpB,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+B,cAAc,CAAEC,iBAAiB,CAAC,CAAGhC,QAAQ,CAAC,kBAAkB,CAAC,CAAE;AAC1E,KAAM,CAACiC,eAAe,CAAEC,kBAAkB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACmC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpC,QAAQ,CAAmG,cAAc,CAAC,CAC9K,KAAM,CAAAqC,cAAc,CAAGpC,MAAM,CAAiB,IAAI,CAAC,CAEnD;AACAC,SAAS,CAAC,IAAM,CACd,GAAImC,cAAc,CAACC,OAAO,CAAE,CAC1BD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAC/D,CACF,CAAC,CAAE,CAACnB,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAoB,uBAAuB,CAAGA,CAACC,YAAoB,CAAEC,cAAsB,GAAc,CACzF;AACA;AACA,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAACC,MAAuB,CAAEH,YAAoB,GAAK,CAC7E,GAAI,MAAO,CAAAG,MAAM,GAAK,QAAQ,CAAE,CAC9B,MAAO,KAAI,CAAE;AACf,CAEA;AACA,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAAC,qBAAqB,CAAInB,KAAa,EAAK,CAC/C;AACA,GAAIA,KAAK,CAACoB,UAAU,CAAC,GAAG,CAAC,CAAE,CACzB,KAAM,CAAAC,OAAO,CAAGrB,KAAK,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAEjD;AACA,GAAIF,OAAO,GAAK,QAAQ,CAAE,CACxB,KAAM,CAAAG,QAAQ,CAAGxB,KAAK,CAACyB,SAAS,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAC1C,KAAM,CAAAC,YAAY,CAAGjD,kBAAkB,CAACkD,IAAI,CAACC,CAAC,EAC5CA,CAAC,CAACC,IAAI,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAACP,QAAQ,CAACD,WAAW,CAAC,CAAC,CAAC,EACrDM,CAAC,CAACG,EAAE,CAACT,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAACP,QAAQ,CAACD,WAAW,CAAC,CAAC,CACpD,CAAC,CAED,GAAII,YAAY,EAAIA,YAAY,CAACM,OAAO,CAAE,CACxC5B,iBAAiB,CAACsB,YAAY,CAACK,EAAE,CAAC,CAClC/B,QAAQ,CAAC,EAAE,CAAC,CACZ,MAAO,WAAW,CACpB,CACF,CAEA;AAAA,IACK,IAAIoB,OAAO,GAAK,QAAQ,EAAIA,OAAO,GAAK,QAAQ,CAAE,CACrDtB,gBAAgB,CAAC,CAAC,CAClBE,QAAQ,CAAC,EAAE,CAAC,CACZ,MAAO,WAAW,CACpB,CACF,CAEA,MAAO,eAAe,CACxB,CAAC,CAED,KAAM,CAAAiC,iBAAiB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACtDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACpC,KAAK,CAAC0B,IAAI,CAAC,CAAC,EAAIxB,YAAY,CAAE,OAEnC;AACA,GAAIF,KAAK,CAACoB,UAAU,CAAC,GAAG,CAAC,CAAE,CACzB,KAAM,CAAAiB,MAAM,CAAGlB,qBAAqB,CAACnB,KAAK,CAAC,CAC3C,GAAIqC,MAAM,GAAK,WAAW,CAAE,CAC1BpC,QAAQ,CAAC,EAAE,CAAC,CACZ,OACF,CACA;AACF,CAEA,MAAO,MAAM,CAAAqC,eAAe,CAACtC,KAAK,CAAC,CACrC,CAAC,CAED,KAAM,CAAAsC,eAAe,CAAG,KAAO,CAAAC,WAAmB,EAAK,KAAAC,qBAAA,CACrD;AACA,GAAI,CAAC/C,cAAc,CAAE,CACnB,KAAM,CAAAE,aAAa,CAAC,CAAC,CACvB,CAEA,KAAM,CAAA8C,WAAwB,CAAG,CAC/BT,EAAE,CAAE,QAAQU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CACxBC,OAAO,CAAEL,WAAW,CACpBM,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCC,MAAM,CAAE,CAAAvD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuC,EAAE,GAAI,MAChC,CAAC,CAEDnC,UAAU,CAAC4C,WAAW,CAAC,CACvBxC,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAgD,SAAS,CAAGP,IAAI,CAACC,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAAO,aAA0B,CAAG,CACjClB,EAAE,CAAE,MAAMiB,SAAS,EAAE,CACrBL,OAAO,CAAE,EAAE,CACXC,MAAM,CAAE,IAAI,CACZM,OAAO,CAAE,IAAI,CACbL,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCK,SAAS,CAAEhD,cAAc,CACzB4C,MAAM,CAAE,CAAAvD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuC,EAAE,GAAI,MAChC,CAAC,CAEDnC,UAAU,CAACqD,aAAa,CAAC,CACzB,CAAAV,qBAAA,CAAA9B,cAAc,CAACC,OAAO,UAAA6B,qBAAA,iBAAtBA,qBAAA,CAAwB5B,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAE9DV,eAAe,CAAC,IAAI,CAAC,CACrBI,kBAAkB,CAAC,IAAI,CAAC,CACxBE,qBAAqB,CAAC,cAAc,CAAC,CAErC,GAAI,KAAA4C,sBAAA,CAAAC,sBAAA,CACF,CAAAD,sBAAA,CAAA3C,cAAc,CAACC,OAAO,UAAA0C,sBAAA,iBAAtBA,sBAAA,CAAwBzC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAE9D;AACA0C,UAAU,CAAC,IAAM9C,qBAAqB,CAAC,qBAAqB,CAAC,CAAE,GAAG,CAAC,CACnE8C,UAAU,CAAC,IAAM9C,qBAAqB,CAAC,oBAAoB,CAAC,CAAE,IAAI,CAAC,CACnE8C,UAAU,CAAC,IAAM9C,qBAAqB,CAAC,mBAAmB,CAAC,CAAE,IAAI,CAAC,CAElE,KAAM,CAAA+C,QAAQ,CAAG,KAAM,CAAAhF,SAAS,CAAC+D,WAAW,CAAEnC,cAAc,CAAC,CAE7D;AACA,KAAM,CAAAqD,SAAsB,CAAG,CAC7BzB,EAAE,CAAE,MAAMiB,SAAS,EAAE,CACrBL,OAAO,CAAEY,QAAQ,CAACE,MAAM,CACxBC,eAAe,CAAEH,QAAQ,CAACG,eAAe,CACzCC,cAAc,CAAEJ,QAAQ,CAACI,cAAc,CACvCR,SAAS,CAAEI,QAAQ,CAACJ,SAAS,EAAIhD,cAAc,CAC/CyC,MAAM,CAAE,IAAI,CACZgB,OAAO,CAAEL,QAAQ,CAACK,OAAO,CACzBC,gBAAgB,CAAEN,QAAQ,CAACM,gBAAgB,CAC3CC,eAAe,CAAEP,QAAQ,CAACO,eAAe,CACzCf,MAAM,CAAE,CAAAvD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuC,EAAE,GAAI,MAAM,CACpCgC,iBAAiB,CAAER,QAAQ,CAACQ,iBAAoB;AAClD,CAAC,CAEDlE,aAAa,CAACoD,aAAa,CAAClB,EAAE,CAAEyB,SAAS,CAAC,CAE1C,CAAAH,sBAAA,CAAA5C,cAAc,CAACC,OAAO,UAAA2C,sBAAA,iBAAtBA,sBAAA,CAAwB1C,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAE,MAAOoD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAE9C;AACA,KAAM,CAAAE,YAAyB,CAAG,CAChCnC,EAAE,CAAE,MAAMiB,SAAS,EAAE,CACrBL,OAAO,CAAE,iNAAiN,CAC1NC,MAAM,CAAE,IAAI,CACZG,MAAM,CAAE,CAAAvD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuC,EAAE,GAAI,MAAM,CACpCgC,iBAAiB,CAAE,IAAO;AAC5B,CAAC,CAEDlE,aAAa,CAACoD,aAAa,CAAClB,EAAE,CAAEmC,YAAY,CAAC,CAC/C,CAAC,OAAS,CACRhE,eAAe,CAAC,KAAK,CAAC,CACtBI,kBAAkB,CAAC,KAAK,CAAC,CACzBE,qBAAqB,CAAC,UAAU,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAA2D,sBAAsB,CAAIP,OAAgC,EAAK,CACnE,GAAI,CAACA,OAAO,EAAIA,OAAO,CAACQ,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAE/C;AACA,KAAM,CAAAC,cAA2E,CAAG,CAAC,CAAC,CAEtFT,OAAO,CAACU,OAAO,CAACrD,MAAM,EAAI,CACxB,GAAI,CAAAsD,QAAgB,CACpB,GAAI,CAAAC,IAAuB,CAE3B,GAAI,MAAO,CAAAvD,MAAM,GAAK,QAAQ,CAAE,CAC9B;AACA,KAAM,CAAAwD,KAAK,CAAGxD,MAAM,CAACwD,KAAK,CAAC,kCAAkC,CAAC,CAC9DF,QAAQ,CAAGE,KAAK,CAAGA,KAAK,CAAC,CAAC,CAAC,CAAChD,IAAI,CAAC,CAAC,CAAGR,MAAM,CAC3CuD,IAAI,CAAGC,KAAK,EAAIA,KAAK,CAAC,CAAC,CAAC,CAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAG,CAAC,CACnD,CAAC,IAAM,CACLF,QAAQ,CAAGtD,MAAM,CAACY,IAAI,EAAIZ,MAAM,CAACsD,QAAQ,EAAI,kBAAkB,CAC/D;AACA,GAAKtD,MAAM,CAAS0D,KAAK,EAAIC,KAAK,CAACC,OAAO,CAAE5D,MAAM,CAAS0D,KAAK,CAAC,EAAK1D,MAAM,CAAS0D,KAAK,CAACP,MAAM,CAAG,CAAC,CAAE,CACrGI,IAAI,CAAIvD,MAAM,CAAS0D,KAAiB,CAC1C,CAAC,IAAM,CACLH,IAAI,CAAGvD,MAAM,CAACuD,IAAI,EAAI,CAAC,CACzB,CACF,CAEA,GAAI,CAACH,cAAc,CAACE,QAAQ,CAAC,CAAE,CAC7BF,cAAc,CAACE,QAAQ,CAAC,CAAG,CAAEA,QAAQ,CAAEI,KAAK,CAAE,GAAI,CAAAG,GAAG,CAAS,CAAE,CAAC,CACnE,CAEA,KAAM,CAAAC,OAAO,CAAIC,CAAS,EAAK,CAC7B,GAAIA,CAAC,EAAIA,CAAC,CAAG,CAAC,CAAE,CAAE;AAChBX,cAAc,CAACE,QAAQ,CAAC,CAACI,KAAK,CAACM,GAAG,CAACD,CAAC,CAAC,CACvC,CACF,CAAC,CAED,GAAIJ,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,CAAE,CACvBA,IAAI,CAACF,OAAO,CAACS,OAAO,CAAC,CACvB,CAAC,IAAM,CACLA,OAAO,CAACP,IAAc,CAAC,CACzB,CACF,CAAC,CAAC,CAEF;AACA,MAAO,CAAAU,MAAM,CAACC,MAAM,CAACd,cAAc,CAAC,CAACe,GAAG,CAACC,KAAK,EAAI,CAChD,KAAM,CAAAC,WAAW,CAAGV,KAAK,CAACW,IAAI,CAACF,KAAK,CAACV,KAAK,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CAAC,CACjE,KAAM,CAAAC,QAAQ,CAAGL,WAAW,CAAClB,MAAM,GAAK,CAAC,CACrC,QAAQkB,WAAW,CAAC,CAAC,CAAC,EAAE,CACxB,SAASA,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE,CAErC;AACA,MAAO,CACLC,IAAI,CAAE,GAAGR,KAAK,CAACd,QAAQ,MAAMoB,QAAQ,EAAE,CACvCG,IAAI,CAAE,gBAAgBC,kBAAkB,CAACV,KAAK,CAACd,QAAQ,CAAC,SAASe,WAAW,CAAC,CAAC,CAAC,EAAE,CACjFU,UAAU,CAAE,IACd,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAIrC,OAAgC,EAAK,CAClE,GAAI,CAACA,OAAO,EAAIA,OAAO,CAACQ,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAE/C;AACA,KAAM,CAAA8B,UAAU,CAAG,GAAI,CAAAC,GAAG,CAA+D,CAAC,CAE1FvC,OAAO,CAACU,OAAO,CAACrD,MAAM,EAAI,CACxB,GAAI,CAAAmF,GAAW,CACf,GAAI,CAAAC,WAAmB,CAEvB,GAAI,MAAO,CAAApF,MAAM,GAAK,QAAQ,CAAE,CAC9BmF,GAAG,CAAGnF,MAAM,CAACE,UAAU,CAAC,MAAM,CAAC,CAAGF,MAAM,CAAG,WAAWA,MAAM,EAAE,CAC9D,GAAI,CACF,KAAM,CAAAqF,MAAM,CAAG,GAAI,CAAAC,GAAG,CAACH,GAAG,CAAC,CAC3BC,WAAW,CAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CACrD,CAAE,KAAM,CACNJ,WAAW,CAAGpF,MAAM,CACtB,CACF,CAAC,IAAM,CACLmF,GAAG,CAAGnF,MAAM,CAACmF,GAAG,EAAI,uCAAuC,CAC3D,GAAI,CACF,KAAM,CAAAE,MAAM,CAAG,GAAI,CAAAC,GAAG,CAACH,GAAG,CAAC,CAC3BC,WAAW,CAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CACrD,CAAE,KAAM,CACNJ,WAAW,CAAGD,GAAG,CACnB,CACF,CAEA;AACA,GAAI,CAACF,UAAU,CAACQ,GAAG,CAACN,GAAG,CAAC,CAAE,CACxBF,UAAU,CAACS,GAAG,CAACP,GAAG,CAAE,CAClBP,IAAI,CAAEQ,WAAW,CACjBP,IAAI,CAAEM,GAAG,CACTJ,UAAU,CAAE,KAAO;AACrB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAApB,KAAK,CAACW,IAAI,CAACW,UAAU,CAACf,MAAM,CAAC,CAAC,CAAC,CAAE;AAC1C,CAAC,CAED;AACA,KAAM,CAAAyB,UAAU,CAAGC,KAAA,EAGb,IAHc,CAAEC,KAAK,CAAEC,UAAU,CAAG,CAG1C,CAAC,CAAAF,KAAA,CACC,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAG7I,QAAQ,CAAC0I,KAAK,CAAC1C,MAAM,EAAI2C,UAAU,CAAC,CAEpE,GAAID,KAAK,CAAC1C,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEnC,KAAM,CAAA8C,YAAY,CAAGF,QAAQ,CAAGF,KAAK,CAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,CAAEJ,UAAU,CAAC,CAClE,KAAM,CAAAK,OAAO,CAAGN,KAAK,CAAC1C,MAAM,CAAG2C,UAAU,CAEzC,KAAM,CAAAM,mBAAmB,CAAGA,CAACnF,CAAsC,CAAE4D,IAAY,GAAK,CACpF;AACA;AACA;AAAA,CACD,CAED,mBACE5G,KAAA,OAAIoI,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EAClDL,YAAY,CAAC9B,GAAG,CAAC,CAACoC,IAAI,CAAEC,KAAK,gBACxBzI,IAAA,OAAAuI,QAAA,CACGC,IAAI,CAAC1B,IAAI,cACZ9G,IAAA,MACE0I,IAAI,CAAEF,IAAI,CAAC1B,IAAK,CAChB6B,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBN,SAAS,CAAC,8DAA8D,CACxEO,KAAK,CAAEL,IAAI,CAACxB,UAAU,CAAG,4BAA4B,CAAG,yBAA0B,CAClF8B,OAAO,CAAEN,IAAI,CAACxB,UAAU,CAAI9D,CAAC,EAAKmF,mBAAmB,CAACnF,CAAC,CAAEsF,IAAI,CAAC1B,IAAK,CAAC,CAAGiC,SAAU,CAAAR,QAAA,CAE5EC,IAAI,CAAC3B,IAAI,CACT,CAAC,cAER7G,IAAA,SAAMsI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEC,IAAI,CAAC3B,IAAI,CAAO,CAC9C,EAdM4B,KAeL,CACL,CAAC,CACLL,OAAO,EAAI,CAACJ,QAAQ,eACnBhI,IAAA,OAAIsI,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvBrI,KAAA,WACE4I,OAAO,CAAEA,CAAA,GAAMb,WAAW,CAAC,IAAI,CAAE,CACjCK,SAAS,CAAC,sEAAsE,CAAAC,QAAA,EACjF,IACG,CAACT,KAAK,CAAC1C,MAAM,CAAG2C,UAAU,CAAC,eAC/B,EAAQ,CAAC,CACP,CACL,CACAK,OAAO,EAAIJ,QAAQ,eAClBhI,IAAA,OAAIsI,SAAS,CAAC,WAAW,CAAAC,QAAA,cACrBvI,IAAA,WACE8I,OAAO,CAAEA,CAAA,GAAMb,WAAW,CAAC,KAAK,CAAE,CACpCK,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAC/E,WAED,CAAQ,CAAC,CACP,CACC,EACP,CAAC,CAET,CAAC,CAED;AACA,KAAM,CAAAS,gBAAgB,CAAG,KAAO,CAAAC,WAAwB,EAAK,CAC3D;AACA,KAAM,CAAAtI,eAAe,CAACsI,WAAW,CAAClG,EAAE,CAAC,CACrC3B,iBAAiB,CAAC6H,WAAW,CAACC,UAAU,EAAI,kBAAkB,CAAC,CAC/D3I,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,CAED,KAAM,CAAA4I,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChClE,OAAO,CAACmE,GAAG,CAAC,sBAAsB,CAAC,CACnC,KAAM,CAAA1I,aAAa,CAAC,CAAC,CACrBH,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,CAED,mBACEL,KAAA,QAAKoI,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAEvEvI,IAAA,CAACJ,WAAW,EACVyJ,SAAS,CAAEhI,eAAgB,CAC3BiI,OAAO,CAAE,CAAC,IAAM,CACd,OAAQ/H,kBAAkB,EACxB,IAAK,qBAAqB,CACxB,MAAO,mCAAmC,CAC5C,IAAK,oBAAoB,CACvB,MAAO,kCAAkC,CAC3C,IAAK,mBAAmB,CACtB,MAAO,gCAAgC,CACzC,QACE,MAAO,kCAAkC,CAC7C,CACF,CAAC,EAAE,CAAE,CACLgI,SAAS,CAAC,SAAS,CACnBC,YAAY,CAAEjI,kBAAmB,CACjCjB,WAAW,CAAEA,WAAY,CAC1B,CAAC,cAGFN,IAAA,CAACL,WAAW,EACV8J,MAAM,CAAEnJ,WAAY,CACpBoJ,QAAQ,CAAEA,CAAA,GAAMnJ,cAAc,CAAC,CAACD,WAAW,CAAE,CAC7CqJ,aAAa,CAAE,CAAAnJ,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuC,EAAE,GAAI,EAAG,CACxC6G,YAAY,CAAEZ,gBAAiB,CAC/Ba,SAAS,CAAEV,aAAc,CAC1B,CAAC,cAGFjJ,KAAA,QAAKoI,SAAS,CAAE,oDAAoDhI,WAAW,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAiI,QAAA,eAElGvI,IAAA,QAAKsI,SAAS,CAAE,UAAU7H,QAAQ,CAAC2E,MAAM,CAAG,CAAC,CAAG,iBAAiB,CAAG,iBAAiB,YAAa,CAAAmD,QAAA,CAC/F9H,QAAQ,CAAC2E,MAAM,GAAK,CAAC,cACpBpF,IAAA,QAAKsI,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDrI,KAAA,QAAKoI,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvI,IAAA,MAAGsI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qBAAmB,CAAG,CAAC,cACjEvI,IAAA,MAAAuI,QAAA,CAAG,wCAAsC,CAAG,CAAC,EAC1C,CAAC,CACH,CAAC,cAENrI,KAAA,QAAAqI,QAAA,EACG9H,QAAQ,CAAC2F,GAAG,CAAEkD,OAAO,eACpBtJ,IAAA,QAEEsI,SAAS,CAAE,QACTgB,OAAO,CAAC1F,MAAM,GAAK,MAAM,CAAG,kBAAkB,CAAG,oBAAoB,EACpE,CAAA2E,QAAA,cAEHrI,KAAA,QACEoI,SAAS,CAAE,2DACTgB,OAAO,CAAC1F,MAAM,GAAK,MAAM,CACrB,wBAAwB,CACxB,kCAAkC,EACrC,CAAA2E,QAAA,eAEHrI,KAAA,QAAKoI,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvI,IAAA,SAAMsI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC5Be,OAAO,CAAC1F,MAAM,GAAK,MAAM,CAAG,KAAK,CAAG,SAAS,CAC1C,CAAC,CACN0F,OAAO,CAACzF,SAAS,eAChB7D,IAAA,SAAMsI,SAAS,CAAE,gBACfgB,OAAO,CAAC1F,MAAM,GAAK,MAAM,CACrB,eAAe,CACf,eAAe,EAClB,CAAA2E,QAAA,CACA,GAAI,CAAA9E,IAAI,CAAC6F,OAAO,CAACzF,SAAS,CAAC,CAACiG,kBAAkB,CAAC,CAAC,CAC7C,CACP,EACE,CAAC,CAGLR,OAAO,CAAC1F,MAAM,GAAK,MAAM,EAAI0F,OAAO,CAAC3F,OAAO,eAC3C3D,IAAA,QAAKsI,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAEe,OAAO,CAAC3F,OAAO,CAAM,CACjE,CAGA2F,OAAO,CAAC1F,MAAM,GAAK,IAAI,eACtB5D,IAAA,QAAAuI,QAAA,CACG,CAAC,CAAAwB,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,GAAM,CACN;AACA,GAAIZ,OAAO,CAACpF,OAAO,EAAI7C,eAAe,EAAI,CAACiI,OAAO,CAAC3F,OAAO,EAAI,CAAC2F,OAAO,CAAC5E,eAAe,EAAI,CAAC4E,OAAO,CAAC3E,cAAc,CAAE,CACjH,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAwF,mBAAmB,CAAGhF,sBAAsB,CAACmE,OAAO,CAACzE,gBAAgB,CAAC,CAC5E,KAAM,CAAAuF,kBAAkB,CAAGnD,qBAAqB,CAACqC,OAAO,CAACxE,eAAe,CAAC,CAEzE;AACA;AACA,KAAM,CAAAuF,kBAAkB,CAAGF,mBAAmB,CAAC/E,MAAM,CAAG,CAAC,CACzD,KAAM,CAAAkF,iBAAiB,CAAGF,kBAAkB,CAAChF,MAAM,CAAG,CAAC,CACvD,KAAM,CAAAmF,kBAAkB,CAAG,CAAC,EAAEjB,OAAO,CAAC5E,eAAe,EAAI4E,OAAO,CAAC5E,eAAe,CAACjC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC/F,KAAM,CAAA+H,iBAAiB,CAAG,CAAC,EAAElB,OAAO,CAAC3E,cAAc,EAAI2E,OAAO,CAAC3E,cAAc,CAAClC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC5F;AACA,KAAM,CAAAgI,cAAc,CAAGnB,OAAO,CAACvE,iBAAiB,EAAK,CAACsF,kBAAkB,EAAI,CAACC,iBAAkB,CAE/F;AACArF,OAAO,CAACmE,GAAG,CAAC,uCAAuCE,OAAO,CAACvG,EAAE,GAAG,CAAE,CAChEsH,kBAAkB,CAClBC,iBAAiB,CACjBC,kBAAkB,CAClBC,iBAAiB,CACjBC,cAAc,CACd1F,iBAAiB,CAAEuE,OAAO,CAACvE,iBAAiB,CAC5C2F,oBAAoB,CAAE,EAAAX,qBAAA,CAAAT,OAAO,CAAC5E,eAAe,UAAAqF,qBAAA,iBAAvBA,qBAAA,CAAyB3E,MAAM,GAAI,CAAC,CAC1DuF,mBAAmB,CAAE,EAAAX,qBAAA,CAAAV,OAAO,CAAC3E,cAAc,UAAAqF,qBAAA,iBAAtBA,qBAAA,CAAwB5E,MAAM,GAAI,CAAC,CACxDwF,oBAAoB,CAAET,mBAAmB,CAAC/E,MAAM,CAChDyF,mBAAmB,CAAET,kBAAkB,CAAChF,MAAM,CAC9C0F,iBAAiB,CAAExB,OAAO,CAAC5E,eAAe,CAAG,QAAQ,CAAG,SAAS,CACjEqG,gBAAgB,CAAEzB,OAAO,CAAC3E,cAAc,CAAG,QAAQ,CAAG,SAAS,CAC/DqG,kBAAkB,CAAE,EAAAf,qBAAA,CAAAX,OAAO,CAACzE,gBAAgB,UAAAoF,qBAAA,iBAAxBA,qBAAA,CAA0B7E,MAAM,GAAI,CAAC,CACzD6F,iBAAiB,CAAE,EAAAf,qBAAA,CAAAZ,OAAO,CAACxE,eAAe,UAAAoF,qBAAA,iBAAvBA,qBAAA,CAAyB9E,MAAM,GAAI,CAAC,CACvD8F,iBAAiB,CAAEb,kBAAkB,CAAG,mBAAmB,CAAGC,iBAAiB,CAAG,kBAAkB,CAAG,cACzG,CAAC,CAAC,CAEF;AACA,KAAM,CAAAa,mBAAmB,CAAG1K,QAAQ,CAAC2K,SAAS,CAACxI,CAAC,EAAIA,CAAC,CAACG,EAAE,GAAKuG,OAAO,CAACvG,EAAE,CAAC,CACxE,GAAI,CAAAjB,YAAY,CAAG,EAAE,CAErB;AACA,IAAK,GAAI,CAAAuJ,CAAC,CAAGF,mBAAmB,CAAG,CAAC,CAAEE,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CACjD,GAAI5K,QAAQ,CAAC4K,CAAC,CAAC,CAACzH,MAAM,GAAK,MAAM,EAAInD,QAAQ,CAAC4K,CAAC,CAAC,CAAC1H,OAAO,CAAE,CACxD7B,YAAY,CAAGrB,QAAQ,CAAC4K,CAAC,CAAC,CAAC1H,OAAO,CAClC,MACF,CACF,CAEAsB,OAAO,CAACmE,GAAG,CAAC,+CAA+C,CAAE,CAAEkC,WAAW,CAAEhC,OAAO,CAACvG,EAAE,CAAEjB,YAAa,CAAC,CAAC,CAEvG;AACA,KAAM,CAAAyJ,UAAU,CAAG,EAAE,CACrB;AACA,GAAI,CAAAC,YAAY,CAAG,EAAE,CAErB;AAEA;AACA,GAAInB,kBAAkB,CAAE,CACtBmB,YAAY,CAAG,eAAe,CAE9BD,UAAU,CAACE,IAAI,cACbvL,KAAA,QAA6BoI,SAAS,CAAC,gGAAgG,CAAAC,QAAA,eACrIrI,KAAA,OAAIoI,SAAS,CAAC,4DAA4D,CAAAC,QAAA,EAAC,+BACtD,CAAC4B,mBAAmB,CAAC/E,MAAM,CAAG,CAAC,CAAG,WAAW,CAAG,UAAU,EAC3E,CAAC,cACLpF,IAAA,CAACN,iBAAiB,EAChBiE,OAAO,CAAE2F,OAAO,CAAC5E,eAAe,EAAI4E,OAAO,CAAC3F,OAAO,EAAI,qDAAsD,CAC7G+H,KAAK,CAAE5J,YAAa,CACpB6J,KAAK,CAAErC,OAAO,CAACnF,SAAU,CACzBJ,MAAM,CAAEuF,OAAO,CAACvF,MAAO,CACxB,CAAC,CAGD,CAAC6H,sBAAA,EAAM,CACN,KAAM,CAAAC,aAAa,CAAG,EAAAD,sBAAA,CAAAtC,OAAO,CAACzE,gBAAgB,UAAA+G,sBAAA,iBAAxBA,sBAAA,CAA0BE,MAAM,CAAC7J,MAAM,EAC3D,MAAO,CAAAA,MAAM,GAAK,QAAQ,EAAIA,MAAM,CAAC8J,cACvC,CAAC,GAAI,EAAE,CAEP9G,OAAO,CAACmE,GAAG,CAAC,0BAA0B,CAAEyC,aAAa,CAACzG,MAAM,CAAC,CAC7DH,OAAO,CAACmE,GAAG,CAAC,oBAAoB,CAAEyC,aAAa,CAACzF,GAAG,CAAC4F,CAAC,GAAK,CACxDzG,QAAQ,CAAGyG,CAAC,CAAYzG,QAAQ,CAChC0G,YAAY,CAAGD,CAAC,CAAYC,YAAY,CACxCC,kBAAkB,CAAE,CAAC,CAAEF,CAAC,CAAYD,cACtC,CAAC,CAAC,CAAC,CAAC,CAEJ;AACA,GAAI,CAAAI,eAAe,CAAGN,aAAa,CAACC,MAAM,CAAC7J,MAAM,EAC/CD,mBAAmB,CAACC,MAAM,CAAYH,YAAY,CACpD,CAAC,CAEDmD,OAAO,CAACmE,GAAG,CAAC,sCAAsC,CAAE+C,eAAe,CAAC/G,MAAM,CAAC,CAE3E;AACA,GAAI+G,eAAe,CAAC/G,MAAM,GAAK,CAAC,GAAKtD,YAAY,CAACQ,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAIhB,YAAY,CAACQ,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAE,CACjIqJ,eAAe,CAAGN,aAAa,CAACC,MAAM,CAAC7J,MAAM,EAC1CA,MAAM,CAAYgK,YAAY,GAAK,OACtC,CAAC,CACDhH,OAAO,CAACmE,GAAG,CAAC,mFAAmF,CAAE+C,eAAe,CAAC/G,MAAM,CAAC,CAC1H,CAEA,MAAO,CAAA+G,eAAe,CAAC/G,MAAM,CAAG,CAAC,EAAIvD,uBAAuB,CAACC,YAAY,CAAEwH,OAAO,CAAC5E,eAAe,EAAI,EAAE,CAAC,cACvGxE,KAAA,QAAKoI,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvI,IAAA,OAAIsI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAAkB,CAAI,CAAC,cAChFvI,IAAA,QAAKsI,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB4D,eAAe,CAAC/F,GAAG,CAAC,CAACnE,MAAM,CAAEwG,KAAK,gBACjCzI,IAAA,CAACH,aAAa,EAAaoC,MAAM,CAAEA,MAAiB,EAAhCwG,KAAkC,CACvD,CAAC,CACC,CAAC,EACH,CAAC,CACJ,IAAI,CACV,CAAC,EAAE,CAAC,cAEJvI,KAAA,QAAKoI,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDvI,IAAA,MAAGsI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACpEvI,IAAA,CAAC4H,UAAU,EAACE,KAAK,CAAEqC,mBAAoB,CAACpC,UAAU,CAAE,CAAE,CAAE,CAAC,EACtD,CAAC,GAtDC,mBAuDJ,CACP,CAAC,CAEH;AACA,CAAC,IAAM,IAAIuC,iBAAiB,CAAE,CAC5BkB,YAAY,CAAG,cAAc,CAE7B,KAAM,CAAAY,YAAY,CAAGhC,kBAAkB,CAAChF,MAAM,CAAG,CAAC,CAAG,oBAAoB,CAAG,mBAAmB,CAE/FmG,UAAU,CAACE,IAAI,cACbvL,KAAA,QAA4BoI,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eACtIrI,KAAA,OAAIoI,SAAS,CAAC,6DAA6D,CAAAC,QAAA,EAAC,+BACvD,CAAC6D,YAAY,EAC9B,CAAC,cACLpM,IAAA,CAACN,iBAAiB,EAChBiE,OAAO,CAAE2F,OAAO,CAAC3E,cAAc,EAAI2E,OAAO,CAAC3F,OAAO,EAAI,oDAAqD,CAC3G+H,KAAK,CAAE5J,YAAa,CACpB6J,KAAK,CAAErC,OAAO,CAACnF,SAAU,CACzBJ,MAAM,CAAEuF,OAAO,CAACvF,MAAO,CACxB,CAAC,cACF7D,KAAA,QAAKoI,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDvI,IAAA,MAAGsI,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACrEvI,IAAA,CAAC4H,UAAU,EAACE,KAAK,CAAEsC,kBAAmB,CAACrC,UAAU,CAAE,CAAE,CAAE,CAAC,EACrD,CAAC,GAbC,kBAcJ,CACP,CAAC,CAEH;AACA,CAAC,IAAM,IAAI0C,cAAc,CAAE,KAAA4B,kBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CACzBnB,YAAY,CAAG,cAAc,CAE7B,KAAM,CAAAoB,SAAS,CAAGtD,OAAO,CAACnF,SAAS,EAAI,MAAM,CAAG;AAChD,KAAM,CAAA0I,SAAS,CAAG,CAAAR,kBAAA,CAAA/C,OAAO,CAACnF,SAAS,UAAAkI,kBAAA,WAAjBA,kBAAA,CAAmBvJ,QAAQ,CAAC,SAAS,CAAC,CAAG,IAAI,CAC/C,CAAAwJ,mBAAA,CAAAhD,OAAO,CAACnF,SAAS,UAAAmI,mBAAA,WAAjBA,mBAAA,CAAmBxJ,QAAQ,CAAC,MAAM,CAAC,CAAG,GAAG,CACzC,CAAAyJ,mBAAA,CAAAjD,OAAO,CAACnF,SAAS,UAAAoI,mBAAA,WAAjBA,mBAAA,CAAmBzJ,QAAQ,CAAC,UAAU,CAAC,CAAG,IAAI,CAC9C,CAAA0J,mBAAA,CAAAlD,OAAO,CAACnF,SAAS,UAAAqI,mBAAA,WAAjBA,mBAAA,CAAmB1J,QAAQ,CAAC,MAAM,CAAC,CAAG,IAAI,CAC1C,CAAA2J,mBAAA,CAAAnD,OAAO,CAACnF,SAAS,UAAAsI,mBAAA,WAAjBA,mBAAA,CAAmB3J,QAAQ,CAAC,QAAQ,CAAC,CAAG,IAAI,CAC5C,CAAA4J,mBAAA,CAAApD,OAAO,CAACnF,SAAS,UAAAuI,mBAAA,WAAjBA,mBAAA,CAAmB5J,QAAQ,CAAC,aAAa,CAAC,CAAG,IAAI,CACjD,CAAA6J,mBAAA,CAAArD,OAAO,CAACnF,SAAS,UAAAwI,mBAAA,WAAjBA,mBAAA,CAAmB7J,QAAQ,CAAC,QAAQ,CAAC,CAAG,IAAI,CAAG,IAAI,CAEnE;AACA,GAAI,CAACzB,eAAe,EAAI,CAACiI,OAAO,CAACpF,OAAO,CAAE,CAC1CqH,UAAU,CAACE,IAAI,cACbvL,KAAA,QAAwBoI,SAAS,CAAC,oGAAoG,CAAAC,QAAA,eACpIrI,KAAA,OAAIoI,SAAS,CAAC,8DAA8D,CAAAC,QAAA,EACzEsE,SAAS,CAAC,uBAAqB,CAACD,SAAS,EACxC,CAAC,cACL5M,IAAA,CAACN,iBAAiB,EAChBiE,OAAO,CAAE2F,OAAO,CAAC3F,OAAO,EAAI,mEAAoE,CAChG+H,KAAK,CAAE5J,YAAa,CACpB6J,KAAK,CAAErC,OAAO,CAACnF,SAAS,EAAI,MAAO,CACnCJ,MAAM,CAAEuF,OAAO,CAACvF,MAAO,CACxB,CAAC,cACF/D,IAAA,QAAKsI,SAAS,CAAC,sCAAsC,CAAAC,QAAA,cACnDvI,IAAA,MAAGsI,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,yFAE9C,CAAG,CAAC,CACD,CAAC,GAdC,cAeJ,CACP,CAAC,CACD,CAEF;AACA,CAAC,IAAM,CACL;AACAiD,YAAY,CAAG,YAAY,CAE3B;AACA,GAAI,CAACnK,eAAe,EAAI,CAACiI,OAAO,CAACpF,OAAO,CAAE,CAC1CqH,UAAU,CAACE,IAAI,cACbvL,KAAA,QAAsBoI,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACpHvI,IAAA,MAAGsI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,cAC9DvI,IAAA,CAACN,iBAAiB,EAChBiE,OAAO,CAAE2F,OAAO,CAAC3F,OAAO,EAAI,sGAAuG,CACnI+H,KAAK,CAAE5J,YAAa,CACpB6J,KAAK,CAAErC,OAAO,CAACnF,SAAS,EAAI,MAAO,CACnCJ,MAAM,CAAEuF,OAAO,CAACvF,MAAO,CACxB,CAAC,GAPK,YAQJ,CACP,CAAC,CACD,CACF,CAEA;AACA,GAAIwH,UAAU,CAACnG,MAAM,CAAG,CAAC,CAAE,CACzB,mBACEpF,IAAA,QAAKsI,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClBgD,UAAU,CACR,CAAC,CAEV,CAEA;AACAtG,OAAO,CAAC6H,IAAI,CAAC,oCAAoC,CAAC,CAClD,mBACE5M,KAAA,QAAKoI,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACnGvI,IAAA,MAAGsI,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,cAC7DvI,IAAA,CAACN,iBAAiB,EAChBiE,OAAO,CAAE2F,OAAO,CAAC3F,OAAO,EAAI,iDAAkD,CAC9E+H,KAAK,CAAE5J,YAAa,CACpB6J,KAAK,CAAErC,OAAO,CAACnF,SAAS,EAAI,QAAS,CACrCJ,MAAM,CAAEuF,OAAO,CAACvF,MAAO,CACxB,CAAC,EACC,CAAC,CAEV,CAAC,EAAE,CAAC,CACD,CACN,EACE,CAAC,EAtQDuF,OAAO,CAACvG,EAuQV,CACN,CAAC,cACF/C,IAAA,QAAK+M,GAAG,CAAEtL,cAAe,CAACuL,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,EAClE,CACN,CACE,CAAC,cAGNhN,KAAA,QAAKoI,SAAS,CAAE,2GAA2GhI,WAAW,CAAG,mBAAmB,CAAG,QAAQ,EAAG,CAAAiI,QAAA,eACxKrI,KAAA,SAAMiN,QAAQ,CAAElK,iBAAkB,CAACqF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxEvI,IAAA,QAAKsI,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCvI,IAAA,UACEoN,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEtM,KAAM,CACbuM,QAAQ,CAAGpK,CAAC,EAAK,CACf,KAAM,CAAAqK,QAAQ,CAAGrK,CAAC,CAACyF,MAAM,CAAC0E,KAAK,CAC/BrM,QAAQ,CAACuM,QAAQ,CAAC,CAClB;AACF,CAAE,CACFC,WAAW,CAAC,+CAA+C,CAC3DlF,SAAS,CAAC,wJAAwJ,CAClKmF,QAAQ,CAAExM,YAAa,CACvB,aAAW,eAAe,CAC3B,CAAC,CACC,CAAC,cAENf,KAAA,QAAKoI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CvI,IAAA,CAACR,WAAW,EACVkO,YAAY,CAAEvM,cAAe,CAC7BwM,aAAa,CAAGC,OAAO,EAAKxM,iBAAiB,CAACwM,OAAO,CAAE,CACvDC,SAAS,CAAE5M,YAAa,CACzB,CAAC,cACFjB,IAAA,WACEoN,IAAI,CAAC,QAAQ,CACbK,QAAQ,CAAExM,YAAY,EAAI,CAACF,KAAK,CAAC0B,IAAI,CAAC,CAAE,CACxC6F,SAAS,CAAC,uMAAuM,CACjNO,KAAK,CAAC,cAAc,CAAAN,QAAA,cAEpBvI,IAAA,SAAAuI,QAAA,CAAOtH,YAAY,CAAG,YAAY,CAAG,MAAM,CAAO,CAAC,CAC7C,CAAC,EACN,CAAC,EACF,CAAC,cACKf,KAAA,QAAKoI,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,iBACnD,CAAC,EAAAlI,qBAAA,CAAAZ,kBAAkB,CAACkD,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACG,EAAE,GAAK5B,cAAc,CAAC,UAAAd,qBAAA,iBAArDA,qBAAA,CAAuDwC,IAAI,GAAI1B,cAAc,EAC1F,CAAC,EACH,CAAC,CAELV,QAAQ,CAAC2E,MAAM,CAAG,CAAC,eAAIpF,IAAA,QAAKsI,SAAS,CAAC,MAAM,CAAM,CAAC,EACjD,CAAC,EACH,CAAC,CAEV,CAOA,QAAS,CAAAwF,GAAGA,CAAAC,KAAA,CAA4C,IAA3C,CAAEzN,WAAW,CAAEC,cAAyB,CAAC,CAAAwN,KAAA,CACpD,mBACE/N,IAAA,CAACG,aAAa,EAACG,WAAW,CAAEA,WAAY,CAACC,cAAc,CAAEA,cAAe,CAAE,CAAC,CAE/E,CAEA,cAAe,CAAAuN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}