/**
 * Utilities for formatting message content and sources
 */

import { Source } from '../types/sources';

// Types for sources (keeping legacy compatibility)
export interface DocumentSource {
  id: string;
  document_id: string;
  filename: string;
  page_numbers?: number[];
  chunk_indices?: number[];
  relevance_score?: number;
}

export interface WebsiteSource {
  id: string;
  website_id: string;
  url: string;
  title?: string;
  chunk_indices?: number[];
  relevance_score?: number;
}

// Extract and normalize document sources from response
export const extractDocumentSources = (response: any): DocumentSource[] => {
  // Check for specific document_sources field
  if (response.document_sources && Array.isArray(response.document_sources)) {
    return response.document_sources.map(normalizeDocumentSource);
  }

  // Fall back to sources field and filter for document sources
  if (response.sources && Array.isArray(response.sources)) {
    return response.sources
      .filter((source: any) => source.document_id || source.filename)
      .map(normalizeDocumentSource);
  }

  return [];
};

// Extract and normalize website sources from response
export const extractWebsiteSources = (response: any): WebsiteSource[] => {
  // Check for specific website_sources field
  if (response.website_sources && Array.isArray(response.website_sources)) {
    return response.website_sources.map(normalizeWebsiteSource);
  }

  // Fall back to sources field and filter for website sources
  if (response.sources && Array.isArray(response.sources)) {
    return response.sources
      .filter((source: any) => source.website_id || source.url)
      .map(normalizeWebsiteSource);
  }

  return [];
};

// Normalize document source to ensure consistent format
const normalizeDocumentSource = (source: any): DocumentSource => {
  // Ensure page_numbers is an array
  let pageNumbers = source.page_numbers || source.pages || [];
  if (!Array.isArray(pageNumbers)) {
    pageNumbers = [pageNumbers];
  }

  // Filter out null/undefined and remove duplicates
  pageNumbers = Array.from(new Set(pageNumbers.filter(Boolean))).map(Number);

  return {
    id: source.id || source.document_id || `doc-${Math.random().toString(36).substring(2, 9)}`,
    document_id: source.document_id || source.id,
    filename: source.filename || source.name || 'Document',
    page_numbers: pageNumbers,
    chunk_indices: Array.isArray(source.chunk_indices) ? source.chunk_indices : 
      (source.chunk_index ? [source.chunk_index] : []),
    relevance_score: source.relevance_score || source.score || 0
  };
};

// Normalize website source to ensure consistent format
const normalizeWebsiteSource = (source: any): WebsiteSource => {
  return {
    id: source.id || source.website_id || `web-${Math.random().toString(36).substring(2, 9)}`,
    website_id: source.website_id || source.id,
    url: source.url || '',
    title: source.title || source.name || '',
    chunk_indices: Array.isArray(source.chunk_indices) ? source.chunk_indices : 
      (source.chunk_index ? [source.chunk_index] : []),
    relevance_score: source.relevance_score || source.score || 0
  };
};

// Format message content for display
export const formatMessageContent = (response: any): string => {
  // Use the most specific content available
  return response.document_answer || 
         response.website_answer || 
         response.answer || 
         response.content || 
         response.text || 
         '';
};

// Extract all sources (document and website) from a response
export const extractAllSources = (response: any): {
  documentSources: Source[],
  websiteSources: Source[],
  allSources: Source[]
} => {
  const rawDocumentSources = extractDocumentSources(response);
  const rawWebsiteSources = extractWebsiteSources(response);

  // Convert document sources to standardized Source objects
  const documentSources: Source[] = rawDocumentSources.map(source => ({
    source_type: 'document' as const,
    filename: source.filename,
    page: source.page_numbers?.[0],
    pages: source.page_numbers,
    chunk_id: source.chunk_indices?.[0]?.toString(),
    relevance_score: source.relevance_score
  }));

  // Convert website sources to standardized Source objects
  const websiteSources: Source[] = rawWebsiteSources.map(source => ({
    source_type: 'website' as const,
    url: source.url,
    name: source.title,
    chunk_id: source.chunk_indices?.[0]?.toString(),
    relevance_score: source.relevance_score
  }));

  // Combine both source types
  const allSources: Source[] = [...documentSources, ...websiteSources];

  return {
    documentSources,
    websiteSources,
    allSources
  };
};
