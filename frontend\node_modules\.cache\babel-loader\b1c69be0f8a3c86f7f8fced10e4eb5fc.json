{"ast": null, "code": "// API endpoints and methods for interacting with the backend\nimport{supabase,saveQuery}from'./supabase';export const API_URL=process.env.REACT_APP_API_URL||'http://localhost:8000';// Category Management Interfaces\n// Category Reassignment Interfaces\n/**\n * Attempts to use the actual backend API but gracefully falls back to a mock response\n * if the backend is not available\n *\n * @param query - The user's question\n * @param model - The LLM model to use (optional, defaults to 'gemini-2.0-flash')\n * @param extractFormat - Format preference for extraction (paragraph, bullet, table)\n * @param useHybridSearch - Whether to use hybrid search (semantic + keyword)\n * @returns Promise with the response containing answer and sources\n */export const sendQuery=async function(query){let model=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'gemini-2.0-flash';let extractFormat=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'paragraph';let useHybridSearch=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;console.log('Processing query:',query,'using model:',model);// Create an error-focused response with NO sources for when LLM/backend fails\n// This prevents showing misleading document references when actual query fails\nconst errorResponse={answer:`No meaningful answer found for '${query}'. The system encountered an error processing your request.`,document_answer:\"\",// Empty string instead of null to satisfy TypeScript\nwebsite_answer:\"\",// Empty string instead of null to satisfy TypeScript\nsources:[],// IMPORTANT: No sources when there's an error to avoid misleading references\ndocument_sources:[],// Empty sources array for documents\nwebsite_sources:[]// Empty sources array for websites\n};// Simple informational response when backend is completely unreachable\n// This is clearly marked as informational and doesn't provide fake sources\nconst connectionErrorResponse={answer:`Backend server at ${API_URL} is not available. Please ensure the server is running with 'uvicorn server:app --reload'.`,document_answer:\"\",// Empty string instead of null to satisfy TypeScript\nwebsite_answer:\"\",// Empty string instead of null to satisfy TypeScript\nsources:[],// NO fabricated document sources\ndocument_sources:[],website_sources:[]};// First, try to use the real backend\ntry{// Check if the backend API is available\nconsole.log('Connecting to backend at:',API_URL);try{// Implement retry mechanism with model fallback\nconst MAX_RETRIES=2;let currentRetry=0;let currentModel=model;let response=null;while(currentRetry<=MAX_RETRIES){// Log the current attempt\nconsole.log(`Attempt ${currentRetry+1}/${MAX_RETRIES+1}: Sending query to: ${API_URL}/api/query with model ${currentModel}`);// Set up timeout control\nconst controller=new AbortController();const timeoutId=setTimeout(()=>controller.abort(),60000);// 60 second timeout - doubled for complex queries\ntry{// Connect directly to the main backend server\nconsole.log(`Sending query to main server: ${API_URL}/api/query`);response=await fetch(`${API_URL}/api/query`,{method:'POST',headers:{'Content-Type':'application/json','Accept':'application/json'},body:JSON.stringify({query,model:currentModel,fallback_enabled:true,extract_format:extractFormat,use_hybrid_search:useHybridSearch,retry_on_timeout:true}),signal:controller.signal});// Clear the timeout to prevent memory leaks\nclearTimeout(timeoutId);// If we got a successful response, break out of the retry loop\nif(response.ok){break;}}catch(error){// Clear the timeout to prevent memory leaks\nclearTimeout(timeoutId);// Handle timeout or network error\nif(error instanceof DOMException&&error.name==='AbortError'){console.warn(`Query timed out with model ${currentModel}, retrying with fallback model...`);}else{console.error(`Fetch error with model ${currentModel}:`,error);}// Fall back to a faster model if available\nif(currentModel==='gemini-2.0-flash'||currentModel==='gemini-2.0-flash'){currentModel='gemini-2.0-flash';// Fall back to faster model\n}else if(currentModel!=='gemini-2.0-flash'){currentModel='gemini-2.0-flash';// Default fallback\n}// If this is the last retry and it failed, throw the error to be caught by the outer try/catch\nif(currentRetry===MAX_RETRIES){throw error;}}currentRetry++;}if(response&&response.ok){const data=await response.json();// Add model information for UI display if it's not present\nif(data&&!data.llm_model){data.llm_model=model;}// Check if response contains valid data\nif(!data.answer){console.error('Invalid response format from server:',data);return{...errorResponse,answer:`The server returned an invalid response format. Please try again later.`};}// Save query to Supabase if enabled\ntry{if(process.env.REACT_APP_SAVE_QUERIES==='true'){const startTime=performance.now();await saveQuery({query_text:query,answer_text:data.answer,llm_model:model,sources:data.sources||[],processing_time:(performance.now()-startTime)/1000});}}catch(error){console.error('Error saving query to Supabase:',error);// Continue even if saving fails\n}return data;}else if(response){// Non-OK response from backend\nconsole.error('Error from backend:',response.status,response.statusText);let errorDetails;try{errorDetails=await response.text();}catch(e){errorDetails='Unable to parse error details';}console.error('Error details:',errorDetails);return{...errorResponse,answer:`Error ${response.status}: ${response.statusText}. ${errorDetails}`};}else{// No response object at all (should never happen with our retry logic)\nreturn{...errorResponse,answer:`The server did not respond. Please try again later.`};}}catch(error){if(error instanceof DOMException&&error.name==='AbortError'){console.error('Query request timed out');// Provide a more helpful suggestion based on the current model\nlet timeoutMessage=`Query timed out after 60 seconds.`;// Suggest a different model based on what's currently being used\nif(model==='gemini-2.0-flash'||model==='gemini-2.0-flash'){timeoutMessage+=` Try a faster model like 'gemini-2.0-flash' or simplify your query.`;}else if(model==='gemini-2.0-flash'){timeoutMessage+=` Try simplifying your query.`;}else{timeoutMessage+=` Try a faster model or simplify your query.`;}return{...errorResponse,answer:timeoutMessage};}// Other types of fetch errors (network issues, etc.)\nconsole.error('Error querying backend:',error);return connectionErrorResponse;}}catch(error){console.error('Error connecting to backend:',error);return connectionErrorResponse;}};/**\n * Upload a document file to the backend\n *\n * @param file - The file to upload\n * @param uploadedBy - Name of the uploader (optional)\n * @returns Promise with the normalized upload response\n */export const uploadDocument=async function(file){let uploadedBy=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'default';let extractTables=arguments.length>2&&arguments[2]!==undefined?arguments[2]:true;let extractImages=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;let extractCharts=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;console.log('Uploading document:',file.name);try{// First, upload to Supabase Storage if enabled\nlet supabaseFilePath=null;let supabaseFileUrl=null;if(process.env.REACT_APP_USE_SUPABASE_STORAGE==='true'){try{// Generate a unique file path to prevent collisions\nconst timestamp=Date.now();const uniqueFilePath=`${uploadedBy}/${timestamp}_${file.name}`;// Upload to Supabase Storage\nconst{data:storageData,error:storageError}=await supabase.storage.from('documents').upload(uniqueFilePath,file,{cacheControl:'3600',upsert:false});if(storageError){console.warn('Supabase Storage upload failed (document will still be processed):',storageError);}else{supabaseFilePath=storageData.path;// Get the public URL\nconst{data:{publicUrl}}=supabase.storage.from('documents').getPublicUrl(supabaseFilePath);supabaseFileUrl=publicUrl;console.log('Uploaded to Supabase Storage:',supabaseFilePath,supabaseFileUrl);}}catch(storageError){console.warn('Supabase Storage upload failed (document will still be processed):',storageError);// Continue with backend upload even if Supabase upload fails\n}}// Create form data for file upload to backend\nconst formData=new FormData();formData.append('file',file);formData.append('uploaded_by',uploadedBy);// Add Supabase storage info if available\nif(supabaseFilePath){formData.append('supabase_file_path',supabaseFilePath);formData.append('supabase_file_url',supabaseFileUrl||'');}// Send the request to the backend\nconst response=await fetch(`${API_URL}/api/upload-document`,{method:'POST',body:formData// Don't set Content-Type header - browser will set it with boundary for FormData\n});if(!response.ok){// Format error response\nconst errorData=await response.json().catch(()=>({detail:response.statusText}));return{success:false,message:errorData.detail||`Upload failed: ${response.status} ${response.statusText}`};}// Process successful response\nconst data=await response.json();console.log('Upload response:',data);// Normalize the response to match our interface\nreturn{success:true,message:data.message,chunks_extracted:data.chunks_extracted,chunks:data.chunks,data:{id:data.document_id||`doc-${Date.now()}`,path:supabaseFilePath||`/documents/${file.name}`,originalResponse:data}};}catch(error){console.error('Error uploading document:',error);return{success:false,message:error instanceof Error?error.message:'Unknown error occurred during upload'};}};/**\n * Add a website URL to be scraped and indexed\n *\n * @param url - The website URL to add\n * @param submittedBy - Name of the submitter (optional)\n * @param extractionOptions - Advanced options for website extraction\n * @returns Promise with the normalized website add response\n */export const addWebsite=async function(url){let submittedBy=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'default';let extractionOptions=arguments.length>2?arguments[2]:undefined;console.log('Adding website:',url,'with options:',extractionOptions);try{const request={url,submitted_by:submittedBy,...extractionOptions};// Send the request to the backend\nconst response=await fetch(`${API_URL}/api/add-website`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(request)});if(!response.ok){// Format error response\nconst errorData=await response.json().catch(()=>({detail:response.statusText}));return{success:false,message:errorData.detail||`Website add failed: ${response.status} ${response.statusText}`};}// Process successful response\nconst data=await response.json();console.log('Website add response:',data);// Normalize the response to match our interface\nreturn{success:true,message:data.message,chunks_extracted:data.chunks_extracted,chunks:data.chunks,data:{id:`web-${Date.now()}`,path:url,originalResponse:data}};}catch(error){console.error('Error adding website:',error);return{success:false,message:error instanceof Error?error.message:'Unknown error occurred while adding website'};}};/**\n * Get document extraction details\n *\n * @param documentId - The ID of the document\n * @returns Promise with the extraction details\n */export const getDocumentExtractionDetails=async documentId=>{console.log('Getting extraction details for document:',documentId);try{const response=await fetch(`${API_URL}/api/documents/${documentId}/extraction-details`);if(!response.ok){console.error(`Failed to get document extraction details: ${response.status} ${response.statusText}`);throw new Error(`Failed to get document extraction details: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting document extraction details:',error);// Return fallback data\nreturn{extractedContent:`Sample extracted content for ${documentId}. This is placeholder text because the actual content could not be retrieved from the server.`,extractionMethod:'Unknown (error occurred)',qualityScore:0,processingTime:0,chunks:0,warnings:['Failed to retrieve extraction details from server'],fallbackReason:error instanceof Error?error.message:'Unknown error'};}};/**\n * Get document content\n *\n * @param documentId - The ID of the document\n * @returns Promise with the document content\n */export const getDocumentContent=async documentId=>{console.log('Getting content for document:',documentId);try{const response=await fetch(`${API_URL}/api/documents/${documentId}/content`);if(!response.ok){console.error(`Failed to get document content: ${response.status} ${response.statusText}`);throw new Error(`Failed to get document content: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting document content:',error);// Return fallback data\nreturn{content:`# Document Content Unavailable\\n\\nThe content for document ${documentId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error?error.message:'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,extraction_method:'Unknown (error occurred)',quality_score:0,processing_time:0,chunks_count:0};}};/**\n * Get website extraction details\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the extraction details\n */export const getWebsiteExtractionDetails=async websiteId=>{console.log('Getting extraction details for website:',websiteId);try{const response=await fetch(`${API_URL}/api/websites/${websiteId}/extraction-details`);if(!response.ok){console.error(`Failed to get website extraction details: ${response.status} ${response.statusText}`);throw new Error(`Failed to get website extraction details: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting website extraction details:',error);// Return fallback data\nreturn{extractedContent:`Sample extracted content for ${websiteId}. This is placeholder text because the actual content could not be retrieved from the server.`,extractionMethod:'Unknown (error occurred)',fallbackHistory:[],contentQuality:0,warnings:['Failed to retrieve extraction details from server'],processingTime:0,chunks:0};}};/**\n * Get website content\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the website content\n */export const getWebsiteContent=async websiteId=>{console.log('Getting content for website:',websiteId);try{const response=await fetch(`${API_URL}/api/websites/${websiteId}/content`);if(!response.ok){console.error(`Failed to get website content: ${response.status} ${response.statusText}`);throw new Error(`Failed to get website content: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting website content:',error);// Return fallback data\nreturn{content:`# Website Content Unavailable\\n\\nThe content for website ${websiteId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error?error.message:'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,extraction_method:'Unknown (error occurred)',quality_score:0,processing_time:0,pages_processed:0,total_links:0};}};/**\n * Submit feedback for an AI answer\n *\n * @param feedbackData - The feedback data containing query, answer, issue type, etc.\n * @returns Promise with the response indicating success or failure\n */export const submitFeedback=async feedbackData=>{console.log('Submitting feedback:',feedbackData);try{const response=await fetch(`${API_URL}/api/feedback`,{method:'POST',headers:{'Content-Type':'application/json','Accept':'application/json'},body:JSON.stringify(feedbackData)});if(!response.ok){console.error(`Failed to submit feedback: ${response.status} ${response.statusText}`);throw new Error(`Failed to submit feedback: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error submitting feedback:',error);return{success:false,message:`Failed to submit feedback: ${error instanceof Error?error.message:'Unknown error'}`};}};/**\n * Get the configured feedback notification emails\n *\n * @returns Promise with the list of configured emails\n */export const getFeedbackEmails=async()=>{try{const response=await fetch(`${API_URL}/api/feedback/emails`);if(!response.ok){console.error(`Failed to get feedback emails: ${response.status} ${response.statusText}`);throw new Error(`Failed to get feedback emails: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting feedback emails:',error);return{emails:[]};}};/**\n * Update the configured feedback notification emails\n *\n * @param emails - The new list of emails to configure for feedback notifications\n * @returns Promise with the response indicating success or failure\n */export const updateFeedbackEmails=async emails=>{try{const response=await fetch(`${API_URL}/api/feedback/emails`,{method:'POST',headers:{'Content-Type':'application/json','Accept':'application/json'},body:JSON.stringify({emails})});if(!response.ok){console.error(`Failed to update feedback emails: ${response.status} ${response.statusText}`);throw new Error(`Failed to update feedback emails: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error updating feedback emails:',error);return{success:false,message:`Failed to update feedback emails: ${error instanceof Error?error.message:'Unknown error'}`};}};/**\n * Get all documents from the backend\n * @returns Promise with the list of documents\n */export const getDocuments=async()=>{console.log('Fetching documents from backend...');try{const response=await fetch(`${API_URL}/api/documents`);if(!response.ok){console.error(`Failed to get documents: ${response.status} ${response.statusText}`);throw new Error(`Failed to get documents: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting documents:',error);return[];// Return empty array on error\n}};/**\n * Get all websites from the backend\n * @returns Promise with the list of websites\n */export const getWebsites=async()=>{console.log('Fetching websites from backend...');try{const response=await fetch(`${API_URL}/api/websites`);if(!response.ok){console.error(`Failed to get websites: ${response.status} ${response.statusText}`);throw new Error(`Failed to get websites: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting websites:',error);return[];// Return empty array on error\n}};// ===== CATEGORY MANAGEMENT API FUNCTIONS =====\n/**\n * Get all categories with hierarchy\n * @returns Promise with the list of categories\n */export const getCategories=async()=>{console.log('Fetching categories from backend...');try{const response=await fetch(`${API_URL}/api/categories/`);if(!response.ok){console.error(`Failed to get categories: ${response.status} ${response.statusText}`);throw new Error(`Failed to get categories: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting categories:',error);return[];// Return empty array on error\n}};/**\n * Create a new category\n * @param category - The category data to create\n * @returns Promise with the creation response\n */export const createCategory=async category=>{console.log('Creating category:',category);try{const response=await fetch(`${API_URL}/api/categories/`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(category)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to create category: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error creating category:',error);throw error;}};/**\n * Update a category\n * @param categoryId - The ID of the category to update\n * @param updates - The updates to apply\n * @returns Promise with the update response\n */export const updateCategory=async(categoryId,updates)=>{console.log('Updating category:',categoryId,updates);try{const response=await fetch(`${API_URL}/api/categories/${categoryId}`,{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify(updates)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to update category: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error updating category:',error);throw error;}};/**\n * Delete a category\n * @param categoryId - The ID of the category to delete\n * @returns Promise with the deletion response\n */export const deleteCategory=async categoryId=>{console.log('Deleting category:',categoryId);try{const response=await fetch(`${API_URL}/api/categories/${categoryId}`,{method:'DELETE'});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to delete category: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error deleting category:',error);throw error;}};/**\n * Update document categories\n * @param documentId - The ID of the document to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */export const updateDocumentCategories=async(documentId,categoryUpdate)=>{console.log('Updating document categories:',documentId,categoryUpdate);try{const response=await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`,{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify(categoryUpdate)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to update document categories: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error updating document categories:',error);throw error;}};/**\n * Bulk update document categories\n * @param documentIds - Array of document IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */export const bulkUpdateDocumentCategories=async(documentIds,categoryUpdate)=>{console.log('Bulk updating document categories:',documentIds,categoryUpdate);try{const response=await fetch(`${API_URL}/api/categories/documents/bulk-update-categories`,{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify({document_ids:documentIds,...categoryUpdate})});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to bulk update document categories: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error bulk updating document categories:',error);throw error;}};/**\n * Get all website categories\n * @returns Promise with the list of website categories\n */export const getWebsiteCategories=async()=>{console.log('Fetching website categories from backend...');try{const response=await fetch(`${API_URL}/api/categories/website-categories/`);if(!response.ok){console.error(`Failed to get website categories: ${response.status} ${response.statusText}`);throw new Error(`Failed to get website categories: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error getting website categories:',error);return[];// Return empty array on error\n}};/**\n * Create a new website category\n * @param category - The website category data to create\n * @returns Promise with the creation response\n */export const createWebsiteCategory=async category=>{console.log('Creating website category:',category);try{const response=await fetch(`${API_URL}/api/categories/website-categories/`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(category)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to create website category: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error creating website category:',error);throw error;}};/**\n * Update website categories\n * @param websiteId - The ID of the website to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */export const updateWebsiteCategories=async(websiteId,categoryUpdate)=>{console.log('Updating website categories:',websiteId,categoryUpdate);try{const response=await fetch(`${API_URL}/api/categories/websites/${websiteId}/categories`,{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify(categoryUpdate)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to update website categories: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error updating website categories:',error);throw error;}};/**\n * Bulk update website categories\n * @param websiteIds - Array of website IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */export const bulkUpdateWebsiteCategories=async(websiteIds,categoryUpdate)=>{console.log('Bulk updating website categories:',websiteIds,categoryUpdate);try{const response=await fetch(`${API_URL}/api/categories/websites/bulk-update-categories`,{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify({website_ids:websiteIds,...categoryUpdate})});if(!response.ok){const errorData=await response.json().catch(()=>({detail:response.statusText}));throw new Error(errorData.detail||`Failed to bulk update website categories: ${response.statusText}`);}const data=await response.json();return data;}catch(error){console.error('Error bulk updating website categories:',error);throw error;}};// Category reassignment for documents\nexport const reassignDocumentCategory=async(documentId,reassignmentRequest)=>{try{console.log(`Reassigning category for document ${documentId}:`,reassignmentRequest);const response=await fetch(`${API_URL}/api/documents/${documentId}/reassign-category`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(reassignmentRequest)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:'Failed to reassign category'}));throw new Error(errorData.detail||`HTTP error! status: ${response.status}`);}const data=await response.json();console.log('Document category reassigned successfully:',data);return data;}catch(error){console.error('Error reassigning document category:',error);throw error;}};// Category reassignment for websites\nexport const reassignWebsiteCategory=async(websiteId,reassignmentRequest)=>{try{console.log(`Reassigning category for website ${websiteId}:`,reassignmentRequest);const response=await fetch(`${API_URL}/api/websites/${websiteId}/reassign-category`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(reassignmentRequest)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:'Failed to reassign category'}));throw new Error(errorData.detail||`HTTP error! status: ${response.status}`);}const data=await response.json();console.log('Website category reassigned successfully:',data);return data;}catch(error){console.error('Error reassigning website category:',error);throw error;}};// Bulk category reassignment for documents\nexport const bulkReassignDocumentCategories=async reassignmentRequest=>{try{console.log(`Bulk reassigning categories for ${reassignmentRequest.item_ids.length} documents:`,reassignmentRequest);const response=await fetch(`${API_URL}/api/documents/bulk-reassign-category`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(reassignmentRequest)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:'Failed to bulk reassign categories'}));throw new Error(errorData.detail||`HTTP error! status: ${response.status}`);}const data=await response.json();console.log('Bulk document categories reassigned successfully:',data);return data;}catch(error){console.error('Error bulk reassigning document categories:',error);throw error;}};// Bulk category reassignment for websites\nexport const bulkReassignWebsiteCategories=async reassignmentRequest=>{try{console.log(`Bulk reassigning categories for ${reassignmentRequest.item_ids.length} websites:`,reassignmentRequest);const response=await fetch(`${API_URL}/api/websites/bulk-reassign-category`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(reassignmentRequest)});if(!response.ok){const errorData=await response.json().catch(()=>({detail:'Failed to bulk reassign categories'}));throw new Error(errorData.detail||`HTTP error! status: ${response.status}`);}const data=await response.json();console.log('Bulk website categories reassigned successfully:',data);return data;}catch(error){console.error('Error bulk reassigning website categories:',error);throw error;}};// Get categories by level for dynamic dropdown population\nexport const getCategoriesByLevel=async function(){let entityType=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'document';let level=arguments.length>1?arguments[1]:undefined;let parentId=arguments.length>2?arguments[2]:undefined;try{let url=`${API_URL}/api/categories/by-level/${entityType}/${level}`;if(parentId){url+=`?parent_id=${parentId}`;}console.log(`Fetching categories for level ${level}, parent: ${parentId||'root'}`);const response=await fetch(url);if(!response.ok){throw new Error(`HTTP error! status: ${response.status}`);}const data=await response.json();console.log(`Retrieved ${data.count} categories for level ${level}`);return data;}catch(error){console.error('Error fetching categories by level:',error);throw error;}};// Get category change history for audit trail\nexport const getCategoryChangeHistory=async(entityType,entityId)=>{try{console.log(`Fetching category change history for ${entityType}: ${entityId}`);const response=await fetch(`${API_URL}/api/category-change-logs/${entityType}/${entityId}`);if(!response.ok){throw new Error(`HTTP error! status: ${response.status}`);}const data=await response.json();console.log(`Retrieved ${data.count} change log entries for ${entityType}: ${entityId}`);return data;}catch(error){console.error('Error fetching category change history:',error);throw error;}};// Helper function to build full category path from IDs\nexport const buildCategoryPath=async(mainCategoryId,categoryId,subCategoryId,minorCategoryId)=>{try{const pathParts=[];// For now, we'll use the IDs as placeholder names\n// In a full implementation, you'd want to fetch the actual category names\nif(mainCategoryId)pathParts.push(mainCategoryId);if(categoryId)pathParts.push(categoryId);if(subCategoryId)pathParts.push(subCategoryId);if(minorCategoryId)pathParts.push(minorCategoryId);return pathParts.join(' → ')||'Uncategorized';}catch(error){console.error('Error building category path:',error);return'Unknown';}};// Document and Website Reprocessing Functions\nexport const reprocessDocument=async function(documentId){let extractionTool=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"auto\";try{console.log(`Reprocessing document ${documentId} with tool: ${extractionTool}`);const response=await fetch(`${API_URL}/api/documents/${documentId}/reprocess?extraction_tool=${extractionTool}`,{method:'POST',headers:{'Content-Type':'application/json'}});if(!response.ok){const errorData=await response.json();throw new Error(errorData.detail||`HTTP error! status: ${response.status}`);}const result=await response.json();console.log('Document reprocess response:',result);return result;}catch(error){console.error('Error reprocessing document:',error);throw error;}};export const reprocessWebsite=async function(websiteId){let extractionTool=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"trafilatura\";try{console.log(`Reprocessing website ${websiteId} with tool: ${extractionTool}`);const response=await fetch(`${API_URL}/api/websites/${websiteId}/reprocess?extraction_tool=${extractionTool}`,{method:'POST',headers:{'Content-Type':'application/json'}});if(!response.ok){const errorData=await response.json();throw new Error(errorData.detail||`HTTP error! status: ${response.status}`);}const result=await response.json();console.log('Website reprocess response:',result);return result;}catch(error){console.error('Error reprocessing website:',error);throw error;}};// -------------------------------------------------------------\n// Temporary helper: unified authenticated fetch used by older\n// components (e.g. ThresholdSettings).  If you later implement a\n// real auth flow, replace this stub accordingly.\n// -------------------------------------------------------------\nexport const fetchWithAuth=async function(url){var _ref;let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const token=localStorage.getItem('auth_token');// Use a simple string-string record to avoid TS7053 index errors\nconst headers={...((_ref=options.headers)!==null&&_ref!==void 0?_ref:{}),'Content-Type':'application/json'};if(token){headers['Authorization']=`Bearer ${token}`;}return fetch(url,{...options,headers});};", "map": {"version": 3, "names": ["supabase", "saveQuery", "API_URL", "process", "env", "REACT_APP_API_URL", "<PERSON><PERSON><PERSON><PERSON>", "query", "model", "arguments", "length", "undefined", "extractFormat", "useHybridSearch", "console", "log", "errorResponse", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "connectionErrorResponse", "MAX_RETRIES", "currentRetry", "currentModel", "response", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "fetch", "method", "headers", "body", "JSON", "stringify", "fallback_enabled", "extract_format", "use_hybrid_search", "retry_on_timeout", "signal", "clearTimeout", "ok", "error", "DOMException", "name", "warn", "data", "json", "llm_model", "REACT_APP_SAVE_QUERIES", "startTime", "performance", "now", "query_text", "answer_text", "processing_time", "status", "statusText", "errorDetails", "text", "e", "timeoutMessage", "uploadDocument", "file", "uploadedBy", "extractTables", "extractImages", "extractCharts", "supabaseFilePath", "supabaseFileUrl", "REACT_APP_USE_SUPABASE_STORAGE", "timestamp", "Date", "uniqueFilePath", "storageData", "storageError", "storage", "from", "upload", "cacheControl", "upsert", "path", "publicUrl", "getPublicUrl", "formData", "FormData", "append", "errorData", "catch", "detail", "success", "message", "chunks_extracted", "chunks", "id", "document_id", "originalResponse", "Error", "addWebsite", "url", "submittedBy", "extractionOptions", "request", "submitted_by", "getDocumentExtractionDetails", "documentId", "extractedContent", "extractionMethod", "qualityScore", "processingTime", "warnings", "fallbackReason", "getDocumentContent", "content", "extraction_method", "quality_score", "chunks_count", "getWebsiteExtractionDetails", "websiteId", "fallbackHistory", "contentQuality", "getWebsiteContent", "pages_processed", "total_links", "submitFeedback", "feedbackData", "getFeedbackEmails", "emails", "updateFeedbackEmails", "getDocuments", "getWebsites", "getCategories", "createCategory", "category", "updateCategory", "categoryId", "updates", "deleteCategory", "updateDocumentCategories", "categoryUpdate", "bulkUpdateDocumentCategories", "documentIds", "document_ids", "getWebsiteCategories", "createWebsiteCategory", "updateWebsiteCategories", "bulkUpdateWebsiteCategories", "websiteIds", "website_ids", "reassignDocumentCategory", "reassignmentRequest", "reassignWebsiteCategory", "bulkReassignDocumentCategories", "item_ids", "bulkReassignWebsiteCategories", "getCategoriesByLevel", "entityType", "level", "parentId", "count", "getCategoryChangeHistory", "entityId", "buildCategoryPath", "mainCategoryId", "subCategoryId", "minorCategoryId", "pathParts", "push", "join", "reprocessDocument", "extractionTool", "result", "reprocessWebsite", "fetchWithAuth", "_ref", "options", "token", "localStorage", "getItem"], "sources": ["C:/IR App/frontend/src/services/api.ts"], "sourcesContent": ["// API endpoints and methods for interacting with the backend\nimport { supabase, saveQuery } from './supabase';\n\nexport const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nexport interface QueryRequest {\n  query: string;\n}\n\nexport interface Source {\n  source_type: string;\n  filename?: string;\n  name?: string;  // For display name of the document\n  page?: number;\n  url?: string;\n  link?: string;  // For document viewer links\n  // Visual content fields\n  content_type?: string;  // \"text\", \"table\", \"image\", \"chart_diagram\"\n  visual_content?: Record<string, any>;  // Visual content metadata\n  storage_url?: string;  // URL for stored visual content\n  display_type?: string;  // \"text\", \"html_table\", \"image\", \"base64_image\"\n}\n\nexport interface QueryResponse {\n  answer: string;  // Combined answer\n  document_answer?: string;  // Answer from document sources only\n  website_answer?: string;  // Answer from website sources only\n  sources: Array<Source>;  // All sources\n  document_sources?: Array<Source>;  // Document sources only\n  website_sources?: Array<Source>;  // Website sources only\n  llm_model?: string;  // The LLM model used for generating the answer\n  llm_fallback_used?: boolean;  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n  visual_content_found?: boolean;  // Whether visual content was found\n  visual_content_types?: string[];  // Types of visual content found\n}\n\nexport interface WebsiteAddRequest {\n  url: string;\n  submitted_by?: string;\n  role?: string;\n  follow_links?: boolean;\n  extraction_depth?: number;\n  extract_images?: boolean;\n  extract_tables?: boolean;\n  max_pages?: number;\n  extractor_type?: string;\n  domain_category?: string;\n  [key: string]: any; // To allow for future extension options\n}\n\nexport interface UploadResponse {\n  success: boolean;\n  message: string;\n  chunks_extracted?: number;\n  chunks?: Array<any>;\n  data?: {\n    id: string;\n    path: string;\n    [key: string]: any;\n  };\n}\n\nexport interface FeedbackData {\n  query: string;  // The user's question\n  answer: string; // The AI's answer\n  issue_type: string; // Inaccurate, Incomplete, Offensive, Too Slow, Other\n  comment?: string; // Optional user comment\n  model?: string; // LLM model used\n  chat_id?: string; // Unique identifier for this chat\n  timestamp: string; // When the feedback was submitted\n}\n\nexport interface FeedbackResponse {\n  success: boolean;\n  message: string;\n}\n\n// Category Management Interfaces\nexport interface Category {\n  id: string;\n  name: string;\n  description?: string;\n  parent_id?: string;\n  level: number;\n  full_path: string;\n  sort_order: number;\n  is_active: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface CategoryHierarchy {\n  id: string;\n  name: string;\n  description?: string;\n  parent_id?: string;\n  level: number;\n  full_path: string;\n  children?: CategoryHierarchy[];\n}\n\nexport interface DocumentCategoryUpdate {\n  main_category?: string;\n  category?: string;\n  sub_category?: string;\n  minor_category?: string;\n}\n\nexport interface WebsiteCategoryUpdate {\n  category?: string;\n  website_category_id?: string;\n}\n\nexport interface WebsiteCategory {\n  id: string;\n  name: string;\n  description?: string;\n  is_active: boolean;\n  sort_order: number;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface CategoryCreateRequest {\n  name: string;\n  description?: string;\n  parent_id?: string;\n  sort_order?: number;\n}\n\nexport interface WebsiteCategoryCreateRequest {\n  name: string;\n  description?: string;\n  sort_order?: number;\n}\n\n// Category Reassignment Interfaces\nexport interface CategoryReassignmentRequest {\n  main_category_id?: string;\n  category_id?: string;\n  sub_category_id?: string;\n  minor_category_id?: string;\n  changed_by?: string;\n  change_reason?: string;\n}\n\nexport interface BulkCategoryReassignmentRequest {\n  item_ids: string[];\n  main_category_id?: string;\n  category_id?: string;\n  sub_category_id?: string;\n  minor_category_id?: string;\n  changed_by?: string;\n  change_reason?: string;\n}\n\nexport interface CategoryChangeLog {\n  id: string;\n  document_id?: string;\n  website_id?: string;\n  old_main_category?: string;\n  old_category?: string;\n  old_sub_category?: string;\n  old_minor_category?: string;\n  new_main_category?: string;\n  new_category?: string;\n  new_sub_category?: string;\n  new_minor_category?: string;\n  changed_by: string;\n  change_reason?: string;\n  changed_at: string;\n}\n\n/**\n * Attempts to use the actual backend API but gracefully falls back to a mock response\n * if the backend is not available\n *\n * @param query - The user's question\n * @param model - The LLM model to use (optional, defaults to 'gemini-2.0-flash')\n * @param extractFormat - Format preference for extraction (paragraph, bullet, table)\n * @param useHybridSearch - Whether to use hybrid search (semantic + keyword)\n * @returns Promise with the response containing answer and sources\n */\nexport const sendQuery = async (\n  query: string,\n  model: string = 'gemini-2.0-flash',\n  extractFormat: string = 'paragraph',\n  useHybridSearch: boolean = true\n): Promise<QueryResponse> => {\n  console.log('Processing query:', query, 'using model:', model);\n\n  // Create an error-focused response with NO sources for when LLM/backend fails\n  // This prevents showing misleading document references when actual query fails\n  const errorResponse: QueryResponse = {\n    answer: `No meaningful answer found for '${query}'. The system encountered an error processing your request.`,\n    document_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    website_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    sources: [], // IMPORTANT: No sources when there's an error to avoid misleading references\n    document_sources: [], // Empty sources array for documents\n    website_sources: [] // Empty sources array for websites\n  };\n\n  // Simple informational response when backend is completely unreachable\n  // This is clearly marked as informational and doesn't provide fake sources\n  const connectionErrorResponse: QueryResponse = {\n    answer: `Backend server at ${API_URL} is not available. Please ensure the server is running with 'uvicorn server:app --reload'.`,\n    document_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    website_answer: \"\", // Empty string instead of null to satisfy TypeScript\n    sources: [], // NO fabricated document sources\n    document_sources: [],\n    website_sources: []\n  };\n\n  // First, try to use the real backend\n  try {\n    // Check if the backend API is available\n    console.log('Connecting to backend at:', API_URL);\n\n    try {\n      // Implement retry mechanism with model fallback\n      const MAX_RETRIES = 2;\n      let currentRetry = 0;\n      let currentModel = model;\n      let response = null;\n      \n      while (currentRetry <= MAX_RETRIES) {\n        // Log the current attempt\n        console.log(`Attempt ${currentRetry + 1}/${MAX_RETRIES + 1}: Sending query to: ${API_URL}/api/query with model ${currentModel}`);\n        \n        // Set up timeout control\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout - doubled for complex queries\n        \n        try {\n          // Connect directly to the main backend server\n          console.log(`Sending query to main server: ${API_URL}/api/query`);\n          \n          response = await fetch(`${API_URL}/api/query`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n              query,\n              model: currentModel,\n              fallback_enabled: true,\n              extract_format: extractFormat,\n              use_hybrid_search: useHybridSearch,\n              retry_on_timeout: true\n            }),\n            signal: controller.signal\n          });\n          \n          // Clear the timeout to prevent memory leaks\n          clearTimeout(timeoutId);\n          \n          // If we got a successful response, break out of the retry loop\n          if (response.ok) {\n            break;\n          }\n        } catch (error) {\n          // Clear the timeout to prevent memory leaks\n          clearTimeout(timeoutId);\n          \n          // Handle timeout or network error\n          if (error instanceof DOMException && error.name === 'AbortError') {\n            console.warn(`Query timed out with model ${currentModel}, retrying with fallback model...`);\n          } else {\n            console.error(`Fetch error with model ${currentModel}:`, error);\n          }\n          \n          // Fall back to a faster model if available\n          if (currentModel === 'gemini-2.0-flash' || currentModel === 'gemini-2.0-flash') {\n            currentModel = 'gemini-2.0-flash'; // Fall back to faster model\n          } else if (currentModel !== 'gemini-2.0-flash') {\n            currentModel = 'gemini-2.0-flash'; // Default fallback\n          }\n          \n          // If this is the last retry and it failed, throw the error to be caught by the outer try/catch\n          if (currentRetry === MAX_RETRIES) {\n            throw error;\n          }\n        }\n        \n        currentRetry++;\n      }\n\n      if (response && response.ok) {\n        const data = await response.json();\n\n        // Add model information for UI display if it's not present\n        if (data && !data.llm_model) {\n          data.llm_model = model;\n        }\n\n        // Check if response contains valid data\n        if (!data.answer) {\n          console.error('Invalid response format from server:', data);\n          return {\n            ...errorResponse,\n            answer: `The server returned an invalid response format. Please try again later.`\n          };\n        }\n\n        // Save query to Supabase if enabled\n        try {\n          if (process.env.REACT_APP_SAVE_QUERIES === 'true') {\n            const startTime = performance.now();\n            await saveQuery({\n              query_text: query,\n              answer_text: data.answer,\n              llm_model: model,\n              sources: data.sources || [],\n              processing_time: (performance.now() - startTime) / 1000\n            });\n          }\n        } catch (error) {\n          console.error('Error saving query to Supabase:', error);\n          // Continue even if saving fails\n        }\n\n        return data as QueryResponse;\n      } else if (response) {\n        // Non-OK response from backend\n        console.error('Error from backend:', response.status, response.statusText);\n\n        let errorDetails;\n        try {\n          errorDetails = await response.text();\n        } catch (e) {\n          errorDetails = 'Unable to parse error details';\n        }\n\n        console.error('Error details:', errorDetails);\n\n        return {\n          ...errorResponse,\n          answer: `Error ${response.status}: ${response.statusText}. ${errorDetails}`\n        };\n      } else {\n        // No response object at all (should never happen with our retry logic)\n        return {\n          ...errorResponse,\n          answer: `The server did not respond. Please try again later.`\n        };\n      }\n    } catch (error) {\n      if (error instanceof DOMException && error.name === 'AbortError') {\n        console.error('Query request timed out');\n\n        // Provide a more helpful suggestion based on the current model\n        let timeoutMessage = `Query timed out after 60 seconds.`;\n\n        // Suggest a different model based on what's currently being used\n        if (model === 'gemini-2.0-flash' || model === 'gemini-2.0-flash') {\n          timeoutMessage += ` Try a faster model like 'gemini-2.0-flash' or simplify your query.`;\n        } else if (model === 'gemini-2.0-flash') {\n          timeoutMessage += ` Try simplifying your query.`;\n        } else {\n          timeoutMessage += ` Try a faster model or simplify your query.`;\n        }\n\n        return {\n          ...errorResponse,\n          answer: timeoutMessage\n        };\n      }\n\n      // Other types of fetch errors (network issues, etc.)\n      console.error('Error querying backend:', error);\n      return connectionErrorResponse;\n    }\n  } catch (error: any) {\n    console.error('Error connecting to backend:', error);\n    return connectionErrorResponse;\n  }\n};\n\n/**\n * Upload a document file to the backend\n *\n * @param file - The file to upload\n * @param uploadedBy - Name of the uploader (optional)\n * @returns Promise with the normalized upload response\n */\nexport const uploadDocument = async (\n  file: File, \n  uploadedBy: string = 'default',\n  extractTables: boolean = true,\n  extractImages: boolean = true,\n  extractCharts: boolean = true\n): Promise<UploadResponse> => {\n  console.log('Uploading document:', file.name);\n\n  try {\n    // First, upload to Supabase Storage if enabled\n    let supabaseFilePath = null;\n    let supabaseFileUrl = null;\n\n    if (process.env.REACT_APP_USE_SUPABASE_STORAGE === 'true') {\n      try {\n        // Generate a unique file path to prevent collisions\n        const timestamp = Date.now();\n        const uniqueFilePath = `${uploadedBy}/${timestamp}_${file.name}`;\n\n        // Upload to Supabase Storage\n        const { data: storageData, error: storageError } = await supabase.storage\n          .from('documents')\n          .upload(uniqueFilePath, file, {\n            cacheControl: '3600',\n            upsert: false\n          });\n\n        if (storageError) {\n          console.warn('Supabase Storage upload failed (document will still be processed):', storageError);\n        } else {\n          supabaseFilePath = storageData.path;\n\n          // Get the public URL\n          const { data: { publicUrl } } = supabase.storage\n            .from('documents')\n            .getPublicUrl(supabaseFilePath);\n\n          supabaseFileUrl = publicUrl;\n          console.log('Uploaded to Supabase Storage:', supabaseFilePath, supabaseFileUrl);\n        }\n      } catch (storageError) {\n        console.warn('Supabase Storage upload failed (document will still be processed):', storageError);\n        // Continue with backend upload even if Supabase upload fails\n      }\n    }\n\n    // Create form data for file upload to backend\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('uploaded_by', uploadedBy);\n\n    // Add Supabase storage info if available\n    if (supabaseFilePath) {\n      formData.append('supabase_file_path', supabaseFilePath);\n      formData.append('supabase_file_url', supabaseFileUrl || '');\n    }\n\n    // Send the request to the backend\n    const response = await fetch(`${API_URL}/api/upload-document`, {\n      method: 'POST',\n      body: formData,\n      // Don't set Content-Type header - browser will set it with boundary for FormData\n    });\n\n    if (!response.ok) {\n      // Format error response\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      return {\n        success: false,\n        message: errorData.detail || `Upload failed: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    // Process successful response\n    const data = await response.json();\n    console.log('Upload response:', data);\n\n    // Normalize the response to match our interface\n    return {\n      success: true,\n      message: data.message,\n      chunks_extracted: data.chunks_extracted,\n      chunks: data.chunks,\n      data: {\n        id: data.document_id || `doc-${Date.now()}`,\n        path: supabaseFilePath || `/documents/${file.name}`,\n        originalResponse: data\n      }\n    };\n  } catch (error) {\n    console.error('Error uploading document:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Unknown error occurred during upload',\n    };\n  }\n};\n\n/**\n * Add a website URL to be scraped and indexed\n *\n * @param url - The website URL to add\n * @param submittedBy - Name of the submitter (optional)\n * @param extractionOptions - Advanced options for website extraction\n * @returns Promise with the normalized website add response\n */\nexport const addWebsite = async (\n  url: string,\n  submittedBy: string = 'default',\n  extractionOptions?: Record<string, any>\n): Promise<UploadResponse> => {\n  console.log('Adding website:', url, 'with options:', extractionOptions);\n\n  try {\n    const request: WebsiteAddRequest = {\n      url,\n      submitted_by: submittedBy,\n      ...extractionOptions\n    };\n\n    // Send the request to the backend\n    const response = await fetch(`${API_URL}/api/add-website`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(request),\n    });\n\n    if (!response.ok) {\n      // Format error response\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      return {\n        success: false,\n        message: errorData.detail || `Website add failed: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    // Process successful response\n    const data = await response.json();\n    console.log('Website add response:', data);\n\n    // Normalize the response to match our interface\n    return {\n      success: true,\n      message: data.message,\n      chunks_extracted: data.chunks_extracted,\n      chunks: data.chunks,\n      data: {\n        id: `web-${Date.now()}`,\n        path: url,\n        originalResponse: data\n      }\n    };\n  } catch (error) {\n    console.error('Error adding website:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Unknown error occurred while adding website',\n    };\n  }\n};\n\n/**\n * Get document extraction details\n *\n * @param documentId - The ID of the document\n * @returns Promise with the extraction details\n */\nexport const getDocumentExtractionDetails = async (documentId: string): Promise<any> => {\n  console.log('Getting extraction details for document:', documentId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/documents/${documentId}/extraction-details`);\n\n    if (!response.ok) {\n      console.error(`Failed to get document extraction details: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get document extraction details: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting document extraction details:', error);\n\n    // Return fallback data\n    return {\n      extractedContent: `Sample extracted content for ${documentId}. This is placeholder text because the actual content could not be retrieved from the server.`,\n      extractionMethod: 'Unknown (error occurred)',\n      qualityScore: 0,\n      processingTime: 0,\n      chunks: 0,\n      warnings: ['Failed to retrieve extraction details from server'],\n      fallbackReason: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\n/**\n * Get document content\n *\n * @param documentId - The ID of the document\n * @returns Promise with the document content\n */\nexport const getDocumentContent = async (documentId: string): Promise<any> => {\n  console.log('Getting content for document:', documentId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/documents/${documentId}/content`);\n\n    if (!response.ok) {\n      console.error(`Failed to get document content: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get document content: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting document content:', error);\n\n    // Return fallback data\n    return {\n      content: `# Document Content Unavailable\\n\\nThe content for document ${documentId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,\n      extraction_method: 'Unknown (error occurred)',\n      quality_score: 0,\n      processing_time: 0,\n      chunks_count: 0\n    };\n  }\n};\n\n/**\n * Get website extraction details\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the extraction details\n */\nexport const getWebsiteExtractionDetails = async (websiteId: string): Promise<any> => {\n  console.log('Getting extraction details for website:', websiteId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/extraction-details`);\n\n    if (!response.ok) {\n      console.error(`Failed to get website extraction details: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website extraction details: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website extraction details:', error);\n\n    // Return fallback data\n    return {\n      extractedContent: `Sample extracted content for ${websiteId}. This is placeholder text because the actual content could not be retrieved from the server.`,\n      extractionMethod: 'Unknown (error occurred)',\n      fallbackHistory: [],\n      contentQuality: 0,\n      warnings: ['Failed to retrieve extraction details from server'],\n      processingTime: 0,\n      chunks: 0\n    };\n  }\n};\n\n/**\n * Get website content\n *\n * @param websiteId - The ID of the website\n * @returns Promise with the website content\n */\nexport const getWebsiteContent = async (websiteId: string): Promise<any> => {\n  console.log('Getting content for website:', websiteId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/content`);\n\n    if (!response.ok) {\n      console.error(`Failed to get website content: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website content: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website content:', error);\n\n    // Return fallback data\n    return {\n      content: `# Website Content Unavailable\\n\\nThe content for website ${websiteId} could not be retrieved from the server.\\n\\n**Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\nPlease try again later or contact support if the problem persists.`,\n      extraction_method: 'Unknown (error occurred)',\n      quality_score: 0,\n      processing_time: 0,\n      pages_processed: 0,\n      total_links: 0\n    };\n  }\n};\n\n/**\n * Submit feedback for an AI answer\n *\n * @param feedbackData - The feedback data containing query, answer, issue type, etc.\n * @returns Promise with the response indicating success or failure\n */\nexport const submitFeedback = async (feedbackData: FeedbackData): Promise<FeedbackResponse> => {\n  console.log('Submitting feedback:', feedbackData);\n\n  try {\n    const response = await fetch(`${API_URL}/api/feedback`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify(feedbackData)\n    });\n\n    if (!response.ok) {\n      console.error(`Failed to submit feedback: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to submit feedback: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error submitting feedback:', error);\n    return {\n      success: false,\n      message: `Failed to submit feedback: ${error instanceof Error ? error.message : 'Unknown error'}`\n    };\n  }\n};\n\n/**\n * Get the configured feedback notification emails\n *\n * @returns Promise with the list of configured emails\n */\nexport const getFeedbackEmails = async (): Promise<{emails: string[]}> => {\n  try {\n    const response = await fetch(`${API_URL}/api/feedback/emails`);\n\n    if (!response.ok) {\n      console.error(`Failed to get feedback emails: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get feedback emails: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting feedback emails:', error);\n    return {\n      emails: []\n    };\n  }\n};\n\n/**\n * Update the configured feedback notification emails\n *\n * @param emails - The new list of emails to configure for feedback notifications\n * @returns Promise with the response indicating success or failure\n */\nexport const updateFeedbackEmails = async (emails: string[]): Promise<FeedbackResponse> => {\n  try {\n    const response = await fetch(`${API_URL}/api/feedback/emails`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      },\n      body: JSON.stringify({ emails })\n    });\n\n    if (!response.ok) {\n      console.error(`Failed to update feedback emails: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to update feedback emails: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating feedback emails:', error);\n    return {\n      success: false,\n      message: `Failed to update feedback emails: ${error instanceof Error ? error.message : 'Unknown error'}`\n    };\n  }\n};\n\n/**\n * Get all documents from the backend\n * @returns Promise with the list of documents\n */\nexport const getDocuments = async (): Promise<any[]> => {\n  console.log('Fetching documents from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/documents`);\n\n    if (!response.ok) {\n      console.error(`Failed to get documents: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get documents: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting documents:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Get all websites from the backend\n * @returns Promise with the list of websites\n */\nexport const getWebsites = async (): Promise<any[]> => {\n  console.log('Fetching websites from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/websites`);\n\n    if (!response.ok) {\n      console.error(`Failed to get websites: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get websites: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting websites:', error);\n    return []; // Return empty array on error\n  }\n};\n\n// ===== CATEGORY MANAGEMENT API FUNCTIONS =====\n\n/**\n * Get all categories with hierarchy\n * @returns Promise with the list of categories\n */\nexport const getCategories = async (): Promise<CategoryHierarchy[]> => {\n  console.log('Fetching categories from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`);\n\n    if (!response.ok) {\n      console.error(`Failed to get categories: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting categories:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Create a new category\n * @param category - The category data to create\n * @returns Promise with the creation response\n */\nexport const createCategory = async (category: CategoryCreateRequest): Promise<any> => {\n  console.log('Creating category:', category);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(category),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to create category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error creating category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update a category\n * @param categoryId - The ID of the category to update\n * @param updates - The updates to apply\n * @returns Promise with the update response\n */\nexport const updateCategory = async (categoryId: string, updates: Partial<Category>): Promise<any> => {\n  console.log('Updating category:', categoryId, updates);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(updates),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating category:', error);\n    throw error;\n  }\n};\n\n/**\n * Delete a category\n * @param categoryId - The ID of the category to delete\n * @returns Promise with the deletion response\n */\nexport const deleteCategory = async (categoryId: string): Promise<any> => {\n  console.log('Deleting category:', categoryId);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {\n      method: 'DELETE',\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to delete category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update document categories\n * @param documentId - The ID of the document to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const updateDocumentCategories = async (\n  documentId: string,\n  categoryUpdate: DocumentCategoryUpdate\n): Promise<any> => {\n  console.log('Updating document categories:', documentId, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(categoryUpdate),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating document categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Bulk update document categories\n * @param documentIds - Array of document IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const bulkUpdateDocumentCategories = async (\n  documentIds: string[],\n  categoryUpdate: DocumentCategoryUpdate\n): Promise<any> => {\n  console.log('Bulk updating document categories:', documentIds, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/documents/bulk-update-categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        document_ids: documentIds,\n        ...categoryUpdate\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to bulk update document categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error bulk updating document categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Get all website categories\n * @returns Promise with the list of website categories\n */\nexport const getWebsiteCategories = async (): Promise<WebsiteCategory[]> => {\n  console.log('Fetching website categories from backend...');\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/website-categories/`);\n\n    if (!response.ok) {\n      console.error(`Failed to get website categories: ${response.status} ${response.statusText}`);\n      throw new Error(`Failed to get website categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error getting website categories:', error);\n    return []; // Return empty array on error\n  }\n};\n\n/**\n * Create a new website category\n * @param category - The website category data to create\n * @returns Promise with the creation response\n */\nexport const createWebsiteCategory = async (category: WebsiteCategoryCreateRequest): Promise<any> => {\n  console.log('Creating website category:', category);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/website-categories/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(category),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to create website category: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error creating website category:', error);\n    throw error;\n  }\n};\n\n/**\n * Update website categories\n * @param websiteId - The ID of the website to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const updateWebsiteCategories = async (\n  websiteId: string,\n  categoryUpdate: WebsiteCategoryUpdate\n): Promise<any> => {\n  console.log('Updating website categories:', websiteId, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/websites/${websiteId}/categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(categoryUpdate),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to update website categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error updating website categories:', error);\n    throw error;\n  }\n};\n\n/**\n * Bulk update website categories\n * @param websiteIds - Array of website IDs to update\n * @param categoryUpdate - The category updates to apply\n * @returns Promise with the update response\n */\nexport const bulkUpdateWebsiteCategories = async (\n  websiteIds: string[],\n  categoryUpdate: WebsiteCategoryUpdate\n): Promise<any> => {\n  console.log('Bulk updating website categories:', websiteIds, categoryUpdate);\n\n  try {\n    const response = await fetch(`${API_URL}/api/categories/websites/bulk-update-categories`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        website_ids: websiteIds,\n        ...categoryUpdate\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: response.statusText }));\n      throw new Error(errorData.detail || `Failed to bulk update website categories: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('Error bulk updating website categories:', error);\n    throw error;\n  }\n};\n\n// Category reassignment for documents\nexport const reassignDocumentCategory = async (\n  documentId: string,\n  reassignmentRequest: CategoryReassignmentRequest\n): Promise<any> => {\n  try {\n    console.log(`Reassigning category for document ${documentId}:`, reassignmentRequest);\n    \n    const response = await fetch(`${API_URL}/api/documents/${documentId}/reassign-category`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(reassignmentRequest),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Failed to reassign category' }));\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    console.log('Document category reassigned successfully:', data);\n    return data;\n  } catch (error) {\n    console.error('Error reassigning document category:', error);\n    throw error;\n  }\n};\n\n// Category reassignment for websites\nexport const reassignWebsiteCategory = async (\n  websiteId: string,\n  reassignmentRequest: CategoryReassignmentRequest\n): Promise<any> => {\n  try {\n    console.log(`Reassigning category for website ${websiteId}:`, reassignmentRequest);\n    \n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/reassign-category`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(reassignmentRequest),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Failed to reassign category' }));\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    console.log('Website category reassigned successfully:', data);\n    return data;\n  } catch (error) {\n    console.error('Error reassigning website category:', error);\n    throw error;\n  }\n};\n\n// Bulk category reassignment for documents\nexport const bulkReassignDocumentCategories = async (\n  reassignmentRequest: BulkCategoryReassignmentRequest\n): Promise<any> => {\n  try {\n    console.log(`Bulk reassigning categories for ${reassignmentRequest.item_ids.length} documents:`, reassignmentRequest);\n    \n    const response = await fetch(`${API_URL}/api/documents/bulk-reassign-category`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(reassignmentRequest),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Failed to bulk reassign categories' }));\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    console.log('Bulk document categories reassigned successfully:', data);\n    return data;\n  } catch (error) {\n    console.error('Error bulk reassigning document categories:', error);\n    throw error;\n  }\n};\n\n// Bulk category reassignment for websites\nexport const bulkReassignWebsiteCategories = async (\n  reassignmentRequest: BulkCategoryReassignmentRequest\n): Promise<any> => {\n  try {\n    console.log(`Bulk reassigning categories for ${reassignmentRequest.item_ids.length} websites:`, reassignmentRequest);\n    \n    const response = await fetch(`${API_URL}/api/websites/bulk-reassign-category`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(reassignmentRequest),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Failed to bulk reassign categories' }));\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    console.log('Bulk website categories reassigned successfully:', data);\n    return data;\n  } catch (error) {\n    console.error('Error bulk reassigning website categories:', error);\n    throw error;\n  }\n};\n\n// Get categories by level for dynamic dropdown population\nexport const getCategoriesByLevel = async (\n  entityType: string = 'document',\n  level: number,\n  parentId?: string\n): Promise<{categories: Category[]; count: number}> => {\n  try {\n    let url = `${API_URL}/api/categories/by-level/${entityType}/${level}`;\n    if (parentId) {\n      url += `?parent_id=${parentId}`;\n    }\n    \n    console.log(`Fetching categories for level ${level}, parent: ${parentId || 'root'}`);\n    \n    const response = await fetch(url);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    console.log(`Retrieved ${data.count} categories for level ${level}`);\n    return data;\n  } catch (error) {\n    console.error('Error fetching categories by level:', error);\n    throw error;\n  }\n};\n\n// Get category change history for audit trail\nexport const getCategoryChangeHistory = async (\n  entityType: 'document' | 'website',\n  entityId: string\n): Promise<{change_logs: CategoryChangeLog[]; count: number}> => {\n  try {\n    console.log(`Fetching category change history for ${entityType}: ${entityId}`);\n    \n    const response = await fetch(`${API_URL}/api/category-change-logs/${entityType}/${entityId}`);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    console.log(`Retrieved ${data.count} change log entries for ${entityType}: ${entityId}`);\n    return data;\n  } catch (error) {\n    console.error('Error fetching category change history:', error);\n    throw error;\n  }\n};\n\n// Helper function to build full category path from IDs\nexport const buildCategoryPath = async (\n  mainCategoryId?: string,\n  categoryId?: string,\n  subCategoryId?: string,\n  minorCategoryId?: string\n): Promise<string> => {\n  try {\n    const pathParts: string[] = [];\n    \n    // For now, we'll use the IDs as placeholder names\n    // In a full implementation, you'd want to fetch the actual category names\n    if (mainCategoryId) pathParts.push(mainCategoryId);\n    if (categoryId) pathParts.push(categoryId);\n    if (subCategoryId) pathParts.push(subCategoryId);\n    if (minorCategoryId) pathParts.push(minorCategoryId);\n    \n    return pathParts.join(' → ') || 'Uncategorized';\n  } catch (error) {\n    console.error('Error building category path:', error);\n    return 'Unknown';\n  }\n};\n\n// Document and Website Reprocessing Functions\nexport const reprocessDocument = async (documentId: string, extractionTool: string = \"auto\"): Promise<any> => {\n  try {\n    console.log(`Reprocessing document ${documentId} with tool: ${extractionTool}`);\n    \n    const response = await fetch(`${API_URL}/api/documents/${documentId}/reprocess?extraction_tool=${extractionTool}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n\n    const result = await response.json();\n    console.log('Document reprocess response:', result);\n    return result;\n  } catch (error) {\n    console.error('Error reprocessing document:', error);\n    throw error;\n  }\n};\n\nexport const reprocessWebsite = async (websiteId: string, extractionTool: string = \"trafilatura\"): Promise<any> => {\n  try {\n    console.log(`Reprocessing website ${websiteId} with tool: ${extractionTool}`);\n    \n    const response = await fetch(`${API_URL}/api/websites/${websiteId}/reprocess?extraction_tool=${extractionTool}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n    }\n\n    const result = await response.json();\n    console.log('Website reprocess response:', result);\n    return result;\n  } catch (error) {\n    console.error('Error reprocessing website:', error);\n    throw error;\n  }\n};\n\n// -------------------------------------------------------------\n// Temporary helper: unified authenticated fetch used by older\n// components (e.g. ThresholdSettings).  If you later implement a\n// real auth flow, replace this stub accordingly.\n// -------------------------------------------------------------\n\nexport const fetchWithAuth = async (\n  url: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const token = localStorage.getItem('auth_token');\n\n  // Use a simple string-string record to avoid TS7053 index errors\n  const headers: Record<string, string> = {\n    ...(options.headers as Record<string, string> | undefined ?? {}),\n    'Content-Type': 'application/json',\n  };\n\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n\n  return fetch(url, {\n    ...options,\n    headers,\n  });\n};\n"], "mappings": "AAAA;AACA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,YAAY,CAEhD,MAAO,MAAM,CAAAC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CA0E/E;AA2DA;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,SAAS,CAAG,cAAAA,CACvBC,KAAa,CAIc,IAH3B,CAAAC,KAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,kBAAkB,IAClC,CAAAG,aAAqB,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,WAAW,IACnC,CAAAI,eAAwB,CAAAJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAE/BK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAER,KAAK,CAAE,cAAc,CAAEC,KAAK,CAAC,CAE9D;AACA;AACA,KAAM,CAAAQ,aAA4B,CAAG,CACnCC,MAAM,CAAE,mCAAmCV,KAAK,6DAA6D,CAC7GW,eAAe,CAAE,EAAE,CAAE;AACrBC,cAAc,CAAE,EAAE,CAAE;AACpBC,OAAO,CAAE,EAAE,CAAE;AACbC,gBAAgB,CAAE,EAAE,CAAE;AACtBC,eAAe,CAAE,EAAG;AACtB,CAAC,CAED;AACA;AACA,KAAM,CAAAC,uBAAsC,CAAG,CAC7CN,MAAM,CAAE,qBAAqBf,OAAO,4FAA4F,CAChIgB,eAAe,CAAE,EAAE,CAAE;AACrBC,cAAc,CAAE,EAAE,CAAE;AACpBC,OAAO,CAAE,EAAE,CAAE;AACbC,gBAAgB,CAAE,EAAE,CACpBC,eAAe,CAAE,EACnB,CAAC,CAED;AACA,GAAI,CACF;AACAR,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEb,OAAO,CAAC,CAEjD,GAAI,CACF;AACA,KAAM,CAAAsB,WAAW,CAAG,CAAC,CACrB,GAAI,CAAAC,YAAY,CAAG,CAAC,CACpB,GAAI,CAAAC,YAAY,CAAGlB,KAAK,CACxB,GAAI,CAAAmB,QAAQ,CAAG,IAAI,CAEnB,MAAOF,YAAY,EAAID,WAAW,CAAE,CAClC;AACAV,OAAO,CAACC,GAAG,CAAC,WAAWU,YAAY,CAAG,CAAC,IAAID,WAAW,CAAG,CAAC,uBAAuBtB,OAAO,yBAAyBwB,YAAY,EAAE,CAAC,CAEhI;AACA,KAAM,CAAAE,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAC,SAAS,CAAGC,UAAU,CAAC,IAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,CAAE,KAAK,CAAC,CAAE;AAE/D,GAAI,CACF;AACAlB,OAAO,CAACC,GAAG,CAAC,iCAAiCb,OAAO,YAAY,CAAC,CAEjEyB,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,YAAY,CAAE,CAC7CgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACZ,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnB/B,KAAK,CACLC,KAAK,CAAEkB,YAAY,CACnBa,gBAAgB,CAAE,IAAI,CACtBC,cAAc,CAAE5B,aAAa,CAC7B6B,iBAAiB,CAAE5B,eAAe,CAClC6B,gBAAgB,CAAE,IACpB,CAAC,CAAC,CACFC,MAAM,CAAEf,UAAU,CAACe,MACrB,CAAC,CAAC,CAEF;AACAC,YAAY,CAACd,SAAS,CAAC,CAEvB;AACA,GAAIH,QAAQ,CAACkB,EAAE,CAAE,CACf,MACF,CACF,CAAE,MAAOC,KAAK,CAAE,CACd;AACAF,YAAY,CAACd,SAAS,CAAC,CAEvB;AACA,GAAIgB,KAAK,WAAY,CAAAC,YAAY,EAAID,KAAK,CAACE,IAAI,GAAK,YAAY,CAAE,CAChElC,OAAO,CAACmC,IAAI,CAAC,8BAA8BvB,YAAY,mCAAmC,CAAC,CAC7F,CAAC,IAAM,CACLZ,OAAO,CAACgC,KAAK,CAAC,0BAA0BpB,YAAY,GAAG,CAAEoB,KAAK,CAAC,CACjE,CAEA;AACA,GAAIpB,YAAY,GAAK,kBAAkB,EAAIA,YAAY,GAAK,kBAAkB,CAAE,CAC9EA,YAAY,CAAG,kBAAkB,CAAE;AACrC,CAAC,IAAM,IAAIA,YAAY,GAAK,kBAAkB,CAAE,CAC9CA,YAAY,CAAG,kBAAkB,CAAE;AACrC,CAEA;AACA,GAAID,YAAY,GAAKD,WAAW,CAAE,CAChC,KAAM,CAAAsB,KAAK,CACb,CACF,CAEArB,YAAY,EAAE,CAChB,CAEA,GAAIE,QAAQ,EAAIA,QAAQ,CAACkB,EAAE,CAAE,CAC3B,KAAM,CAAAK,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAElC;AACA,GAAID,IAAI,EAAI,CAACA,IAAI,CAACE,SAAS,CAAE,CAC3BF,IAAI,CAACE,SAAS,CAAG5C,KAAK,CACxB,CAEA;AACA,GAAI,CAAC0C,IAAI,CAACjC,MAAM,CAAE,CAChBH,OAAO,CAACgC,KAAK,CAAC,sCAAsC,CAAEI,IAAI,CAAC,CAC3D,MAAO,CACL,GAAGlC,aAAa,CAChBC,MAAM,CAAE,yEACV,CAAC,CACH,CAEA;AACA,GAAI,CACF,GAAId,OAAO,CAACC,GAAG,CAACiD,sBAAsB,GAAK,MAAM,CAAE,CACjD,KAAM,CAAAC,SAAS,CAAGC,WAAW,CAACC,GAAG,CAAC,CAAC,CACnC,KAAM,CAAAvD,SAAS,CAAC,CACdwD,UAAU,CAAElD,KAAK,CACjBmD,WAAW,CAAER,IAAI,CAACjC,MAAM,CACxBmC,SAAS,CAAE5C,KAAK,CAChBY,OAAO,CAAE8B,IAAI,CAAC9B,OAAO,EAAI,EAAE,CAC3BuC,eAAe,CAAE,CAACJ,WAAW,CAACC,GAAG,CAAC,CAAC,CAAGF,SAAS,EAAI,IACrD,CAAC,CAAC,CACJ,CACF,CAAE,MAAOR,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD;AACF,CAEA,MAAO,CAAAI,IAAI,CACb,CAAC,IAAM,IAAIvB,QAAQ,CAAE,CACnB;AACAb,OAAO,CAACgC,KAAK,CAAC,qBAAqB,CAAEnB,QAAQ,CAACiC,MAAM,CAAEjC,QAAQ,CAACkC,UAAU,CAAC,CAE1E,GAAI,CAAAC,YAAY,CAChB,GAAI,CACFA,YAAY,CAAG,KAAM,CAAAnC,QAAQ,CAACoC,IAAI,CAAC,CAAC,CACtC,CAAE,MAAOC,CAAC,CAAE,CACVF,YAAY,CAAG,+BAA+B,CAChD,CAEAhD,OAAO,CAACgC,KAAK,CAAC,gBAAgB,CAAEgB,YAAY,CAAC,CAE7C,MAAO,CACL,GAAG9C,aAAa,CAChBC,MAAM,CAAE,SAASU,QAAQ,CAACiC,MAAM,KAAKjC,QAAQ,CAACkC,UAAU,KAAKC,YAAY,EAC3E,CAAC,CACH,CAAC,IAAM,CACL;AACA,MAAO,CACL,GAAG9C,aAAa,CAChBC,MAAM,CAAE,qDACV,CAAC,CACH,CACF,CAAE,MAAO6B,KAAK,CAAE,CACd,GAAIA,KAAK,WAAY,CAAAC,YAAY,EAAID,KAAK,CAACE,IAAI,GAAK,YAAY,CAAE,CAChElC,OAAO,CAACgC,KAAK,CAAC,yBAAyB,CAAC,CAExC;AACA,GAAI,CAAAmB,cAAc,CAAG,mCAAmC,CAExD;AACA,GAAIzD,KAAK,GAAK,kBAAkB,EAAIA,KAAK,GAAK,kBAAkB,CAAE,CAChEyD,cAAc,EAAI,qEAAqE,CACzF,CAAC,IAAM,IAAIzD,KAAK,GAAK,kBAAkB,CAAE,CACvCyD,cAAc,EAAI,8BAA8B,CAClD,CAAC,IAAM,CACLA,cAAc,EAAI,6CAA6C,CACjE,CAEA,MAAO,CACL,GAAGjD,aAAa,CAChBC,MAAM,CAAEgD,cACV,CAAC,CACH,CAEA;AACAnD,OAAO,CAACgC,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAAvB,uBAAuB,CAChC,CACF,CAAE,MAAOuB,KAAU,CAAE,CACnBhC,OAAO,CAACgC,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,CAAAvB,uBAAuB,CAChC,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA2C,cAAc,CAAG,cAAAA,CAC5BC,IAAU,CAKkB,IAJ5B,CAAAC,UAAkB,CAAA3D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,IAC9B,CAAA4D,aAAsB,CAAA5D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,IAC7B,CAAA6D,aAAsB,CAAA7D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,IAC7B,CAAA8D,aAAsB,CAAA9D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAE7BK,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEoD,IAAI,CAACnB,IAAI,CAAC,CAE7C,GAAI,CACF;AACA,GAAI,CAAAwB,gBAAgB,CAAG,IAAI,CAC3B,GAAI,CAAAC,eAAe,CAAG,IAAI,CAE1B,GAAItE,OAAO,CAACC,GAAG,CAACsE,8BAA8B,GAAK,MAAM,CAAE,CACzD,GAAI,CACF;AACA,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACpB,GAAG,CAAC,CAAC,CAC5B,KAAM,CAAAqB,cAAc,CAAG,GAAGT,UAAU,IAAIO,SAAS,IAAIR,IAAI,CAACnB,IAAI,EAAE,CAEhE;AACA,KAAM,CAAEE,IAAI,CAAE4B,WAAW,CAAEhC,KAAK,CAAEiC,YAAa,CAAC,CAAG,KAAM,CAAA/E,QAAQ,CAACgF,OAAO,CACtEC,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAACL,cAAc,CAAEV,IAAI,CAAE,CAC5BgB,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,KACV,CAAC,CAAC,CAEJ,GAAIL,YAAY,CAAE,CAChBjE,OAAO,CAACmC,IAAI,CAAC,oEAAoE,CAAE8B,YAAY,CAAC,CAClG,CAAC,IAAM,CACLP,gBAAgB,CAAGM,WAAW,CAACO,IAAI,CAEnC;AACA,KAAM,CAAEnC,IAAI,CAAE,CAAEoC,SAAU,CAAE,CAAC,CAAGtF,QAAQ,CAACgF,OAAO,CAC7CC,IAAI,CAAC,WAAW,CAAC,CACjBM,YAAY,CAACf,gBAAgB,CAAC,CAEjCC,eAAe,CAAGa,SAAS,CAC3BxE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAEyD,gBAAgB,CAAEC,eAAe,CAAC,CACjF,CACF,CAAE,MAAOM,YAAY,CAAE,CACrBjE,OAAO,CAACmC,IAAI,CAAC,oEAAoE,CAAE8B,YAAY,CAAC,CAChG;AACF,CACF,CAEA;AACA,KAAM,CAAAS,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEvB,IAAI,CAAC,CAC7BqB,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEtB,UAAU,CAAC,CAE1C;AACA,GAAII,gBAAgB,CAAE,CACpBgB,QAAQ,CAACE,MAAM,CAAC,oBAAoB,CAAElB,gBAAgB,CAAC,CACvDgB,QAAQ,CAACE,MAAM,CAAC,mBAAmB,CAAEjB,eAAe,EAAI,EAAE,CAAC,CAC7D,CAEA;AACA,KAAM,CAAA9C,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,sBAAsB,CAAE,CAC7DgC,MAAM,CAAE,MAAM,CACdE,IAAI,CAAEoD,QACN;AACF,CAAC,CAAC,CAEF,GAAI,CAAC7D,QAAQ,CAACkB,EAAE,CAAE,CAChB;AACA,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,MAAO,CACLiC,OAAO,CAAE,KAAK,CACdC,OAAO,CAAEJ,SAAS,CAACE,MAAM,EAAI,kBAAkBlE,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EACvF,CAAC,CACH,CAEA;AACA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEmC,IAAI,CAAC,CAErC;AACA,MAAO,CACL4C,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE7C,IAAI,CAAC6C,OAAO,CACrBC,gBAAgB,CAAE9C,IAAI,CAAC8C,gBAAgB,CACvCC,MAAM,CAAE/C,IAAI,CAAC+C,MAAM,CACnB/C,IAAI,CAAE,CACJgD,EAAE,CAAEhD,IAAI,CAACiD,WAAW,EAAI,OAAOvB,IAAI,CAACpB,GAAG,CAAC,CAAC,EAAE,CAC3C6B,IAAI,CAAEb,gBAAgB,EAAI,cAAcL,IAAI,CAACnB,IAAI,EAAE,CACnDoD,gBAAgB,CAAElD,IACpB,CACF,CAAC,CACH,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,CACLgD,OAAO,CAAE,KAAK,CACdC,OAAO,CAAEjD,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,sCACpD,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAO,UAAU,CAAG,cAAAA,CACxBC,GAAW,CAGiB,IAF5B,CAAAC,WAAmB,CAAA/F,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,IAC/B,CAAAgG,iBAAuC,CAAAhG,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEvCG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEwF,GAAG,CAAE,eAAe,CAAEE,iBAAiB,CAAC,CAEvE,GAAI,CACF,KAAM,CAAAC,OAA0B,CAAG,CACjCH,GAAG,CACHI,YAAY,CAAEH,WAAW,CACzB,GAAGC,iBACL,CAAC,CAED;AACA,KAAM,CAAA9E,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB,CAAE,CACzDgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACoE,OAAO,CAC9B,CAAC,CAAC,CAEF,GAAI,CAAC/E,QAAQ,CAACkB,EAAE,CAAE,CAChB;AACA,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,MAAO,CACLiC,OAAO,CAAE,KAAK,CACdC,OAAO,CAAEJ,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAC5F,CAAC,CACH,CAEA;AACA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEmC,IAAI,CAAC,CAE1C;AACA,MAAO,CACL4C,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE7C,IAAI,CAAC6C,OAAO,CACrBC,gBAAgB,CAAE9C,IAAI,CAAC8C,gBAAgB,CACvCC,MAAM,CAAE/C,IAAI,CAAC+C,MAAM,CACnB/C,IAAI,CAAE,CACJgD,EAAE,CAAE,OAAOtB,IAAI,CAACpB,GAAG,CAAC,CAAC,EAAE,CACvB6B,IAAI,CAAEkB,GAAG,CACTH,gBAAgB,CAAElD,IACpB,CACF,CAAC,CACH,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,CACLgD,OAAO,CAAE,KAAK,CACdC,OAAO,CAAEjD,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,6CACpD,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAa,4BAA4B,CAAG,KAAO,CAAAC,UAAkB,EAAmB,CACtF/F,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAE8F,UAAU,CAAC,CAEnE,GAAI,CACF,KAAM,CAAAlF,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB2G,UAAU,qBAAqB,CAAC,CAEzF,GAAI,CAAClF,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,8CAA8CnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACrG,KAAM,IAAI,CAAAwC,KAAK,CAAC,8CAA8C1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACtF,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,4CAA4C,CAAEA,KAAK,CAAC,CAElE;AACA,MAAO,CACLgE,gBAAgB,CAAE,gCAAgCD,UAAU,+FAA+F,CAC3JE,gBAAgB,CAAE,0BAA0B,CAC5CC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBhB,MAAM,CAAE,CAAC,CACTiB,QAAQ,CAAE,CAAC,mDAAmD,CAAC,CAC/DC,cAAc,CAAErE,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,eAC3D,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAqB,kBAAkB,CAAG,KAAO,CAAAP,UAAkB,EAAmB,CAC5E/F,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAE8F,UAAU,CAAC,CAExD,GAAI,CACF,KAAM,CAAAlF,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB2G,UAAU,UAAU,CAAC,CAE9E,GAAI,CAAClF,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,mCAAmCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1F,KAAM,IAAI,CAAAwC,KAAK,CAAC,mCAAmC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC3E,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAEvD;AACA,MAAO,CACLuE,OAAO,CAAE,8DAA8DR,UAAU,0DAA0D/D,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,eAAe,wEAAwE,CAC3QuB,iBAAiB,CAAE,0BAA0B,CAC7CC,aAAa,CAAE,CAAC,CAChB5D,eAAe,CAAE,CAAC,CAClB6D,YAAY,CAAE,CAChB,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,2BAA2B,CAAG,KAAO,CAAAC,SAAiB,EAAmB,CACpF5G,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAE2G,SAAS,CAAC,CAEjE,GAAI,CACF,KAAM,CAAA/F,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,iBAAiBwH,SAAS,qBAAqB,CAAC,CAEvF,GAAI,CAAC/F,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,6CAA6CnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACpG,KAAM,IAAI,CAAAwC,KAAK,CAAC,6CAA6C1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACrF,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CAEjE;AACA,MAAO,CACLgE,gBAAgB,CAAE,gCAAgCY,SAAS,+FAA+F,CAC1JX,gBAAgB,CAAE,0BAA0B,CAC5CY,eAAe,CAAE,EAAE,CACnBC,cAAc,CAAE,CAAC,CACjBV,QAAQ,CAAE,CAAC,mDAAmD,CAAC,CAC/DD,cAAc,CAAE,CAAC,CACjBhB,MAAM,CAAE,CACV,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA4B,iBAAiB,CAAG,KAAO,CAAAH,SAAiB,EAAmB,CAC1E5G,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAE2G,SAAS,CAAC,CAEtD,GAAI,CACF,KAAM,CAAA/F,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,iBAAiBwH,SAAS,UAAU,CAAC,CAE5E,GAAI,CAAC/F,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,kCAAkCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACzF,KAAM,IAAI,CAAAwC,KAAK,CAAC,kCAAkC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1E,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CAEtD;AACA,MAAO,CACLuE,OAAO,CAAE,4DAA4DK,SAAS,0DAA0D5E,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,eAAe,wEAAwE,CACxQuB,iBAAiB,CAAE,0BAA0B,CAC7CC,aAAa,CAAE,CAAC,CAChB5D,eAAe,CAAE,CAAC,CAClBmE,eAAe,CAAE,CAAC,CAClBC,WAAW,CAAE,CACf,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,cAAc,CAAG,KAAO,CAAAC,YAA0B,EAAgC,CAC7FnH,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEkH,YAAY,CAAC,CAEjD,GAAI,CACF,KAAM,CAAAtG,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,eAAe,CAAE,CACtDgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACZ,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC2F,YAAY,CACnC,CAAC,CAAC,CAEF,GAAI,CAACtG,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,8BAA8BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACrF,KAAM,IAAI,CAAAwC,KAAK,CAAC,8BAA8B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACtE,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CACLgD,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,8BAA8BjD,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,eAAe,EACjG,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAmC,iBAAiB,CAAG,KAAAA,CAAA,GAAyC,CACxE,GAAI,CACF,KAAM,CAAAvG,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,sBAAsB,CAAC,CAE9D,GAAI,CAACyB,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,kCAAkCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACzF,KAAM,IAAI,CAAAwC,KAAK,CAAC,kCAAkC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1E,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,CACLqF,MAAM,CAAE,EACV,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,oBAAoB,CAAG,KAAO,CAAAD,MAAgB,EAAgC,CACzF,GAAI,CACF,KAAM,CAAAxG,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,sBAAsB,CAAE,CAC7DgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACZ,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAE6F,MAAO,CAAC,CACjC,CAAC,CAAC,CAEF,GAAI,CAACxG,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,qCAAqCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC5F,KAAM,IAAI,CAAAwC,KAAK,CAAC,qCAAqC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC7E,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,MAAO,CACLgD,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,qCAAqCjD,KAAK,WAAY,CAAAuD,KAAK,CAAGvD,KAAK,CAACiD,OAAO,CAAG,eAAe,EACxG,CAAC,CACH,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAsC,YAAY,CAAG,KAAAA,CAAA,GAA4B,CACtDvH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CAEjD,GAAI,CACF,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,gBAAgB,CAAC,CAExD,GAAI,CAACyB,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,4BAA4BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACnF,KAAM,IAAI,CAAAwC,KAAK,CAAC,4BAA4B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACpE,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,EAAE,CAAE;AACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAwF,WAAW,CAAG,KAAAA,CAAA,GAA4B,CACrDxH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAEhD,GAAI,CACF,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,eAAe,CAAC,CAEvD,GAAI,CAACyB,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,2BAA2BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAClF,KAAM,IAAI,CAAAwC,KAAK,CAAC,2BAA2B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACnE,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,EAAE,CAAE;AACb,CACF,CAAC,CAED;AAEA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAyF,aAAa,CAAG,KAAAA,CAAA,GAA0C,CACrEzH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAElD,GAAI,CACF,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB,CAAC,CAE1D,GAAI,CAACyB,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,6BAA6BnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACpF,KAAM,IAAI,CAAAwC,KAAK,CAAC,6BAA6B1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACrE,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,EAAE,CAAE;AACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA0F,cAAc,CAAG,KAAO,CAAAC,QAA+B,EAAmB,CACrF3H,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE0H,QAAQ,CAAC,CAE3C,GAAI,CACF,KAAM,CAAA9G,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB,CAAE,CACzDgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACmG,QAAQ,CAC/B,CAAC,CAAC,CAEF,GAAI,CAAC9G,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,8BAA8BlE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1F,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA4F,cAAc,CAAG,KAAAA,CAAOC,UAAkB,CAAEC,OAA0B,GAAmB,CACpG9H,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE4H,UAAU,CAAEC,OAAO,CAAC,CAEtD,GAAI,CACF,KAAM,CAAAjH,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,mBAAmByI,UAAU,EAAE,CAAE,CACtEzG,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACsG,OAAO,CAC9B,CAAC,CAAC,CAEF,GAAI,CAACjH,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,8BAA8BlE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1F,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA+F,cAAc,CAAG,KAAO,CAAAF,UAAkB,EAAmB,CACxE7H,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE4H,UAAU,CAAC,CAE7C,GAAI,CACF,KAAM,CAAAhH,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,mBAAmByI,UAAU,EAAE,CAAE,CACtEzG,MAAM,CAAE,QACV,CAAC,CAAC,CAEF,GAAI,CAACP,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,8BAA8BlE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1F,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAgG,wBAAwB,CAAG,KAAAA,CACtCjC,UAAkB,CAClBkC,cAAsC,GACrB,CACjBjI,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAE8F,UAAU,CAAEkC,cAAc,CAAC,CAExE,GAAI,CACF,KAAM,CAAApH,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,6BAA6B2G,UAAU,aAAa,CAAE,CAC3F3E,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACyG,cAAc,CACrC,CAAC,CAAC,CAEF,GAAI,CAACpH,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,yCAAyClE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACrG,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAkG,4BAA4B,CAAG,KAAAA,CAC1CC,WAAqB,CACrBF,cAAsC,GACrB,CACjBjI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEkI,WAAW,CAAEF,cAAc,CAAC,CAE9E,GAAI,CACF,KAAM,CAAApH,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kDAAkD,CAAE,CACzFgC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnB4G,YAAY,CAAED,WAAW,CACzB,GAAGF,cACL,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACpH,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,8CAA8ClE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC1G,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAChE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAqG,oBAAoB,CAAG,KAAAA,CAAA,GAAwC,CAC1ErI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC,CAE1D,GAAI,CACF,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,qCAAqC,CAAC,CAE7E,GAAI,CAACyB,QAAQ,CAACkB,EAAE,CAAE,CAChB/B,OAAO,CAACgC,KAAK,CAAC,qCAAqCnB,QAAQ,CAACiC,MAAM,IAAIjC,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC5F,KAAM,IAAI,CAAAwC,KAAK,CAAC,qCAAqC1E,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAC7E,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,MAAO,EAAE,CAAE;AACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAsG,qBAAqB,CAAG,KAAO,CAAAX,QAAsC,EAAmB,CACnG3H,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAE0H,QAAQ,CAAC,CAEnD,GAAI,CACF,KAAM,CAAA9G,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,qCAAqC,CAAE,CAC5EgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACmG,QAAQ,CAC/B,CAAC,CAAC,CAEF,GAAI,CAAC9G,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,sCAAsClE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CAClG,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAuG,uBAAuB,CAAG,KAAAA,CACrC3B,SAAiB,CACjBqB,cAAqC,GACpB,CACjBjI,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAE2G,SAAS,CAAEqB,cAAc,CAAC,CAEtE,GAAI,CACF,KAAM,CAAApH,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,4BAA4BwH,SAAS,aAAa,CAAE,CACzFxF,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACyG,cAAc,CACrC,CAAC,CAAC,CAEF,GAAI,CAACpH,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,wCAAwClE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACpG,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAwG,2BAA2B,CAAG,KAAAA,CACzCC,UAAoB,CACpBR,cAAqC,GACpB,CACjBjI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEwI,UAAU,CAAER,cAAc,CAAC,CAE5E,GAAI,CACF,KAAM,CAAApH,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,iDAAiD,CAAE,CACxFgC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBkH,WAAW,CAAED,UAAU,CACvB,GAAGR,cACL,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACpH,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAElE,QAAQ,CAACkC,UAAW,CAAC,CAAC,CAAC,CACtF,KAAM,IAAI,CAAAwC,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,6CAA6ClE,QAAQ,CAACkC,UAAU,EAAE,CAAC,CACzG,CAEA,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClC,MAAO,CAAAD,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,yCAAyC,CAAEA,KAAK,CAAC,CAC/D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA2G,wBAAwB,CAAG,KAAAA,CACtC5C,UAAkB,CAClB6C,mBAAgD,GAC/B,CACjB,GAAI,CACF5I,OAAO,CAACC,GAAG,CAAC,qCAAqC8F,UAAU,GAAG,CAAE6C,mBAAmB,CAAC,CAEpF,KAAM,CAAA/H,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB2G,UAAU,oBAAoB,CAAE,CACvF3E,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACoH,mBAAmB,CAC1C,CAAC,CAAC,CAEF,GAAI,CAAC/H,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAE,6BAA8B,CAAC,CAAC,CAAC,CAChG,KAAM,IAAI,CAAAQ,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC/E,CAEA,KAAM,CAAAV,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEmC,IAAI,CAAC,CAC/D,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA6G,uBAAuB,CAAG,KAAAA,CACrCjC,SAAiB,CACjBgC,mBAAgD,GAC/B,CACjB,GAAI,CACF5I,OAAO,CAACC,GAAG,CAAC,oCAAoC2G,SAAS,GAAG,CAAEgC,mBAAmB,CAAC,CAElF,KAAM,CAAA/H,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,iBAAiBwH,SAAS,oBAAoB,CAAE,CACrFxF,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACoH,mBAAmB,CAC1C,CAAC,CAAC,CAEF,GAAI,CAAC/H,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAE,6BAA8B,CAAC,CAAC,CAAC,CAChG,KAAM,IAAI,CAAAQ,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC/E,CAEA,KAAM,CAAAV,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAEmC,IAAI,CAAC,CAC9D,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA8G,8BAA8B,CAAG,KAC5C,CAAAF,mBAAoD,EACnC,CACjB,GAAI,CACF5I,OAAO,CAACC,GAAG,CAAC,mCAAmC2I,mBAAmB,CAACG,QAAQ,CAACnJ,MAAM,aAAa,CAAEgJ,mBAAmB,CAAC,CAErH,KAAM,CAAA/H,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,uCAAuC,CAAE,CAC9EgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACoH,mBAAmB,CAC1C,CAAC,CAAC,CAEF,GAAI,CAAC/H,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAE,oCAAqC,CAAC,CAAC,CAAC,CACvG,KAAM,IAAI,CAAAQ,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC/E,CAEA,KAAM,CAAAV,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAEmC,IAAI,CAAC,CACtE,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,6CAA6C,CAAEA,KAAK,CAAC,CACnE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAgH,6BAA6B,CAAG,KAC3C,CAAAJ,mBAAoD,EACnC,CACjB,GAAI,CACF5I,OAAO,CAACC,GAAG,CAAC,mCAAmC2I,mBAAmB,CAACG,QAAQ,CAACnJ,MAAM,YAAY,CAAEgJ,mBAAmB,CAAC,CAEpH,KAAM,CAAA/H,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,sCAAsC,CAAE,CAC7EgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACoH,mBAAmB,CAC1C,CAAC,CAAC,CAEF,GAAI,CAAC/H,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAACyC,KAAK,CAAC,KAAO,CAAEC,MAAM,CAAE,oCAAqC,CAAC,CAAC,CAAC,CACvG,KAAM,IAAI,CAAAQ,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC/E,CAEA,KAAM,CAAAV,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAEmC,IAAI,CAAC,CACrE,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,4CAA4C,CAAEA,KAAK,CAAC,CAClE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAiH,oBAAoB,CAAG,cAAAA,CAAA,CAImB,IAHrD,CAAAC,UAAkB,CAAAvJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,UAAU,IAC/B,CAAAwJ,KAAa,CAAAxJ,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,IACb,CAAAuJ,QAAiB,CAAAzJ,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEjB,GAAI,CACF,GAAI,CAAA4F,GAAG,CAAG,GAAGrG,OAAO,4BAA4B8J,UAAU,IAAIC,KAAK,EAAE,CACrE,GAAIC,QAAQ,CAAE,CACZ3D,GAAG,EAAI,cAAc2D,QAAQ,EAAE,CACjC,CAEApJ,OAAO,CAACC,GAAG,CAAC,iCAAiCkJ,KAAK,aAAaC,QAAQ,EAAI,MAAM,EAAE,CAAC,CAEpF,KAAM,CAAAvI,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAACsE,GAAG,CAAC,CAEjC,GAAI,CAAC5E,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAwD,KAAK,CAAC,uBAAuB1E,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC3D,CAEA,KAAM,CAAAV,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,aAAamC,IAAI,CAACiH,KAAK,yBAAyBF,KAAK,EAAE,CAAC,CACpE,MAAO,CAAA/G,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAsH,wBAAwB,CAAG,KAAAA,CACtCJ,UAAkC,CAClCK,QAAgB,GAC+C,CAC/D,GAAI,CACFvJ,OAAO,CAACC,GAAG,CAAC,wCAAwCiJ,UAAU,KAAKK,QAAQ,EAAE,CAAC,CAE9E,KAAM,CAAA1I,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,6BAA6B8J,UAAU,IAAIK,QAAQ,EAAE,CAAC,CAE7F,GAAI,CAAC1I,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAwD,KAAK,CAAC,uBAAuB1E,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC3D,CAEA,KAAM,CAAAV,IAAI,CAAG,KAAM,CAAAvB,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAClCrC,OAAO,CAACC,GAAG,CAAC,aAAamC,IAAI,CAACiH,KAAK,2BAA2BH,UAAU,KAAKK,QAAQ,EAAE,CAAC,CACxF,MAAO,CAAAnH,IAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,yCAAyC,CAAEA,KAAK,CAAC,CAC/D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAwH,iBAAiB,CAAG,KAAAA,CAC/BC,cAAuB,CACvB5B,UAAmB,CACnB6B,aAAsB,CACtBC,eAAwB,GACJ,CACpB,GAAI,CACF,KAAM,CAAAC,SAAmB,CAAG,EAAE,CAE9B;AACA;AACA,GAAIH,cAAc,CAAEG,SAAS,CAACC,IAAI,CAACJ,cAAc,CAAC,CAClD,GAAI5B,UAAU,CAAE+B,SAAS,CAACC,IAAI,CAAChC,UAAU,CAAC,CAC1C,GAAI6B,aAAa,CAAEE,SAAS,CAACC,IAAI,CAACH,aAAa,CAAC,CAChD,GAAIC,eAAe,CAAEC,SAAS,CAACC,IAAI,CAACF,eAAe,CAAC,CAEpD,MAAO,CAAAC,SAAS,CAACE,IAAI,CAAC,KAAK,CAAC,EAAI,eAAe,CACjD,CAAE,MAAO9H,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,SAAS,CAClB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA+H,iBAAiB,CAAG,cAAAA,CAAOhE,UAAkB,CAAoD,IAAlD,CAAAiE,cAAsB,CAAArK,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,MAAM,CACzF,GAAI,CACFK,OAAO,CAACC,GAAG,CAAC,yBAAyB8F,UAAU,eAAeiE,cAAc,EAAE,CAAC,CAE/E,KAAM,CAAAnJ,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,kBAAkB2G,UAAU,8BAA8BiE,cAAc,EAAE,CAAE,CACjH5I,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,GAAI,CAACR,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAkD,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC/E,CAEA,KAAM,CAAAmH,MAAM,CAAG,KAAM,CAAApJ,QAAQ,CAACwB,IAAI,CAAC,CAAC,CACpCrC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEgK,MAAM,CAAC,CACnD,MAAO,CAAAA,MAAM,CACf,CAAE,MAAOjI,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAkI,gBAAgB,CAAG,cAAAA,CAAOtD,SAAiB,CAA2D,IAAzD,CAAAoD,cAAsB,CAAArK,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,aAAa,CAC9F,GAAI,CACFK,OAAO,CAACC,GAAG,CAAC,wBAAwB2G,SAAS,eAAeoD,cAAc,EAAE,CAAC,CAE7E,KAAM,CAAAnJ,QAAQ,CAAG,KAAM,CAAAM,KAAK,CAAC,GAAG/B,OAAO,iBAAiBwH,SAAS,8BAA8BoD,cAAc,EAAE,CAAE,CAC/G5I,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,GAAI,CAACR,QAAQ,CAACkB,EAAE,CAAE,CAChB,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAhE,QAAQ,CAACwB,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAkD,KAAK,CAACV,SAAS,CAACE,MAAM,EAAI,uBAAuBlE,QAAQ,CAACiC,MAAM,EAAE,CAAC,CAC/E,CAEA,KAAM,CAAAmH,MAAM,CAAG,KAAM,CAAApJ,QAAQ,CAACwB,IAAI,CAAC,CAAC,CACpCrC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEgK,MAAM,CAAC,CAClD,MAAO,CAAAA,MAAM,CACf,CAAE,MAAOjI,KAAK,CAAE,CACdhC,OAAO,CAACgC,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AAEA,MAAO,MAAM,CAAAmI,aAAa,CAAG,cAAAA,CAC3B1E,GAAW,CAEW,KAAA2E,IAAA,IADtB,CAAAC,OAAoB,CAAA1K,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAEzB,KAAM,CAAA2K,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAEhD;AACA,KAAM,CAAAnJ,OAA+B,CAAG,CACtC,KAAA+I,IAAA,CAAIC,OAAO,CAAChJ,OAAO,UAAA+I,IAAA,UAAAA,IAAA,CAA0C,CAAC,CAAC,CAAC,CAChE,cAAc,CAAE,kBAClB,CAAC,CAED,GAAIE,KAAK,CAAE,CACTjJ,OAAO,CAAC,eAAe,CAAC,CAAG,UAAUiJ,KAAK,EAAE,CAC9C,CAEA,MAAO,CAAAnJ,KAAK,CAACsE,GAAG,CAAE,CAChB,GAAG4E,OAAO,CACVhJ,OACF,CAAC,CAAC,CACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}